#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KPI数据合并工具
智能合并小区级别KPI文件，实现正确的364行列追加合并
排除网元级别数据，确保业务逻辑正确
"""

import pandas as pd
import os
import glob
import warnings
warnings.filterwarnings('ignore')

# 全局配置
EXCLUDE_KEYWORDS = ['波束信息ID', '可替换单元ID']
CELL_FIELDS = ['CU小区配置ID', 'nrPhysicalCellDUId', 'NRCarrierId']
BASE_FIELDS = ['开始时间', '结束时间', '粒度', 'SubnetWork ID', 'SubnetWork Name', 'ManagedElement ID', '管理网元']
EXPECTED_ROWS = None

# 字段映射（中英文对照）
FIELD_MAPPINGS = {
    # 排除关键字映射
    'EXCLUDE_KEYWORDS': {
        '波束信息ID': ['波束信息ID'],
        '可替换单元ID': ['可替换单元ID', 'ReplaceableUnitId', 'Replaceable Unit ID']
    },
    
    # 小区字段映射
    'CELL_FIELDS': {
        'CU小区配置ID': ['CU小区配置ID'],
        'nrPhysicalCellDUId': ['nrPhysicalCellDUId'],
        'NRCarrierId': ['NRCarrierId']
    },
    
    # 基础字段映射
    'BASE_FIELDS': {
        '开始时间': ['开始时间', 'Start Time', 'StartTime'],
        '结束时间': ['结束时间', 'End Time', 'EndTime'],
        '粒度': ['粒度', 'Granularity Period', 'GranularityPeriod'],
        'SubnetWork ID': ['SubnetWork ID', 'SubnetworkId', '子网ID', 'Subnetwork ID'],
        'SubnetWork Name': ['SubnetWork Name', 'SubnetworkName', '子网名称', 'Subnetwork Name'],
        'ManagedElement ID': ['ManagedElement ID', 'ManagedElementId', '网元ID', 'Managed Element ID'],
        '管理网元': ['管理网元']
    }
}

def find_excel_files():
    """查找所有Excel文件"""
    excel_files = [f for f in glob.glob("*.xlsx")
                   if not f.startswith('~$')]

    print(f"找到 {len(excel_files)} 个Excel文件")
    return excel_files

def is_valid_file(file_path):
    """检查文件是否有效"""
    try:
        df = pd.read_excel(file_path, sheet_name=0)

        # 检查排除关键字
        columns_str = ' '.join(df.columns.astype(str))
        has_exclude = any(keyword in columns_str for keyword in EXCLUDE_KEYWORDS)

        # 检查是否有数据
        has_data = len(df) > 0
        
        # 检查是否有小区级别字段
        has_cell_fields = any(field in df.columns for field in CELL_FIELDS)

        if has_exclude:
            return False, "包含排除关键字"
        elif not has_data:
            return False, "无数据行"
        elif not has_cell_fields:
            return False, "网元级别(无小区字段)"
        else:
            return True, "有效文件"

    except Exception as e:
        return False, f"读取错误: {str(e)}"

def filter_valid_files(excel_files):
    """筛选有效文件（只保留小区级别文件）"""
    valid_files = []
    element_level_files = []

    print("\n检查文件有效性...")
    for file in excel_files:
        is_valid, reason = is_valid_file(file)

        if is_valid:
            # 读取文件，获取小区字段和行数信息
            df = pd.read_excel(file, sheet_name=0)
            cell_fields = [field for field in CELL_FIELDS if field in df.columns]
            
            valid_files.append({
                'file': file,
                'df': df,
                'cell_fields': cell_fields,
                'rows': len(df)
            })
            print(f"✓ {file[:50]}... ({len(df)} 行, 字段: {cell_fields})")
        else:
            if reason == "网元级别(无小区字段)":
                element_level_files.append(file)
            print(f"✗ {file[:50]}... ({reason})")

    print(f"\n小区级别文件: {len(valid_files)} 个")
    print(f"排除的文件: {len(excel_files) - len(valid_files)} 个")
    
    return valid_files

def detect_fields(valid_files):
    """智能检测所有文件的字段，更新全局字段变量"""
    global EXCLUDE_KEYWORDS, CELL_FIELDS, BASE_FIELDS
    
    print("\n智能检测字段...")
    
    # 收集所有文件的列名
    all_columns = set()
    for file in valid_files:
        try:
            df = pd.read_excel(file, sheet_name=0)
            # 打印每个文件的列名，帮助调试
            print(f"  文件 {os.path.basename(file)} 的列名: {list(df.columns)}")
            
            # 确保列名被正确添加为字符串
            for col in df.columns:
                all_columns.add(str(col).strip())  # 添加并去除可能的空格
            
        except Exception as e:
            print(f"  读取文件 {file} 失败: {str(e)}")
    
    # 打印收集到的所有列名，帮助调试
    print(f"  收集到的所有列名: {sorted(list(all_columns))}")
    
    # 检测并更新每种字段类型
    for field_type, mapping in FIELD_MAPPINGS.items():
        detected_fields = []
        
        print(f"\n  检测{field_type}字段...")
        for field_key, field_variants in mapping.items():
            found = False
            found_variant = None
            
            # 打印正在检测的字段变体，帮助调试
            print(f"    检测字段 {field_key} 的变体: {field_variants}")
            
            for variant in field_variants:
                # 确保变体也被转换为字符串并去除空格
                variant_str = str(variant).strip()
                if variant_str in all_columns:
                    found_variant = variant_str
                    found = True
                    print(f"    ✓ 找到变体: {variant_str}")
                    break
                else:
                    print(f"    ✗ 未找到变体: {variant_str}")
            
            if found:
                detected_fields.append(found_variant)
                print(f"  检测到{field_type}字段: {found_variant} (对应 {field_key})")
            else:
                # 对于排除关键字，如果没找到就不添加
                if field_type == 'EXCLUDE_KEYWORDS':
                    print(f"  未检测到排除关键字: {field_key} 的任何变体")
                    # 添加原始关键字，确保排除关键字不为空
                    detected_fields.append(field_key)
                    print(f"  添加原始排除关键字: {field_key}")
                # 对于小区字段，如果没找到就报错
                elif field_type == 'CELL_FIELDS':
                    error_msg = f"错误: 未找到小区字段 {field_key} 的任何变体。请确保文件中包含以下字段之一: {field_variants}"
                    print(f"  {error_msg}")
                    raise ValueError(error_msg)
                else:
                    # 基础字段，如果没找到就保留原始字段名
                    detected_fields.append(field_key)
                    print(f"  警告: 未找到{field_type}字段 {field_key} 的任何变体")
        
        # 更新全局变量
        if field_type == 'EXCLUDE_KEYWORDS':
            EXCLUDE_KEYWORDS = detected_fields
            print(f"  更新排除关键字: {EXCLUDE_KEYWORDS}")
        elif field_type == 'CELL_FIELDS':
            if detected_fields:
                CELL_FIELDS = detected_fields
                print(f"  更新小区字段: {CELL_FIELDS}")
            else:
                error_msg = "错误: 未检测到任何小区字段，无法进行合并"
                print(f"  {error_msg}")
                raise ValueError(error_msg)
        elif field_type == 'BASE_FIELDS':
            if len(detected_fields) == len(mapping):
                BASE_FIELDS = detected_fields
                print(f"  更新基础字段: {BASE_FIELDS}")
            else:
                print(f"  警告: 检测到的基础字段数量不匹配，保留原始基础字段")
    
    return {
        'EXCLUDE_KEYWORDS': EXCLUDE_KEYWORDS,
        'CELL_FIELDS': CELL_FIELDS,
        'BASE_FIELDS': BASE_FIELDS
    }

def analyze_file_types(valid_files):
    """分析文件类型，区分小区级别和网元级别"""
    cell_level_files = []
    element_level_files = []

    print(f"\n智能文件分析:")
    for file in valid_files:
        df = pd.read_excel(file, sheet_name=0)

        # 检查是否有小区级别字段
        has_cell_fields = [field for field in CELL_FIELDS if field in df.columns]

        if has_cell_fields:  # 只要有小区字段就认为是小区级别文件
            cell_level_files.append({
                'file': file,
                'df': df,
                'cell_fields': has_cell_fields,
                'rows': len(df)
            })
            print(f"小区级别: {os.path.basename(file)[:40]}... ({len(df)} 行, 字段: {has_cell_fields})")
        else:
            element_level_files.append(file)
            reason = f"网元级别(无小区字段)"
            print(f" 排除: {os.path.basename(file)[:40]}... ({reason})")

    print(f"  小区级别文件: {len(cell_level_files)} 个")
    print(f"  排除的文件: {len(element_level_files)} 个")

    return cell_level_files, element_level_files

def create_merge_key(df, fields):
    """创建合并键"""
    available_fields = [field for field in fields if field in df.columns]
    if available_fields:
        return df[available_fields].astype(str).apply(lambda x: '|'.join(x.values), axis=1)
    return None

def calculate_match_rate(left_keys, right_keys):
    """计算匹配率"""
    if len(left_keys) == 0:
        return 0
    return len(set(left_keys) & set(right_keys)) / len(set(left_keys)) * 100


def clean_duplicate_columns(merged_data, suffix_pattern='_追加'):
    """清理所有带有指定后缀的列，但保留数据源列"""
    # 找出所有包含后缀的列，但排除数据源列
    columns_to_drop = [col for col in merged_data.columns 
                      if suffix_pattern in col and not col.startswith('数据源')]
    
    # 如果有需要删除的列，就删除
    if columns_to_drop:
        print(f"  删除的列: {columns_to_drop}")
        merged_data = merged_data.drop(columns=columns_to_drop)
        print(f"  清理了 {len(columns_to_drop)} 个带有'{suffix_pattern}'后缀的列")

    # 返回清理后的数据和删除的列数
    return merged_data, len(columns_to_drop)

# def clean_duplicate_columns(merged_data, suffix_pattern='_追加'):
#     """清理重复的基础字段，保留小区字段"""
#     cols_to_drop = []
#     for col in merged_data.columns:
#         # 如果列名里有指定后缀（如"_追加"）
#         if suffix_pattern in col:
#             # 取出原始字段名（去掉后缀部分）
#             original_col = col.split(suffix_pattern)[0]
#             # 只删除基础字段，保留小区字段
#             if original_col in BASE_FIELDS:
#                 # 标记为待删除
#                 cols_to_drop.append(col)

#     # 如果有需要删除的列，就删除
#     if cols_to_drop:
#         merged_data = merged_data.drop(cols_to_drop, axis=1)

#     # 返回清理后的数据和删除的列数
#     return merged_data, len(cols_to_drop)

def get_cell_field_type(df):
    """获取数据框的小区字段类型"""
    for field in CELL_FIELDS:
        if field in df.columns:
            return field
    return None

def prepare_merge_keys(merged_data, df, base_field_type, current_field_type):
    """准备合并键"""
    left_on = BASE_FIELDS.copy()
    right_on = BASE_FIELDS.copy()

    # 如果字段类型相同，直接使用相同字段合并
    if current_field_type == base_field_type:
        left_on.append(current_field_type)
        right_on.append(current_field_type)
    else:
        # 不同字段类型，使用各自的字段
        left_on.append(base_field_type)
        right_on.append(current_field_type)
    return left_on, right_on

def merge_single_file_data(merged_data, df, left_on, right_on, file_index):
    """合并单个文件数据"""
    before_cols = len(merged_data.columns)
    before_rows = len(merged_data)

    # 检查匹配率
    left_keys = create_merge_key(merged_data[left_on].drop_duplicates(), left_on)
    right_keys = create_merge_key(df[right_on].drop_duplicates(), right_on)

    match_rate = calculate_match_rate(left_keys, right_keys)
    print(f"  合并键匹配率: {match_rate:.1f}%")

    if match_rate == 100:  # 要求100%匹配率
        # 使用内连接合并
        merged_data = pd.merge(
            merged_data,
            df,
            left_on=left_on,
            right_on=right_on,
            how='inner',
            suffixes=('', f'_追加{file_index}')
        )

        # 清理重复的基础字段和小区字段
        merged_data, _ = clean_duplicate_columns(merged_data)

        after_cols = len(merged_data.columns)
        after_rows = len(merged_data)

        print(f"  合并成功: {after_rows} 行 ({before_rows} -> {after_rows})")
        print(f"  新增列数: {after_cols - before_cols}")

        return merged_data, True
    else:
        print(f" 跳过：匹配率不是100% ({match_rate:.1f}%)")
        return merged_data, False

def merge_all_files(cell_level_files, base_file_info):
    """合并所有小区级别文件，使用第一个文件作为基础"""
    # 初始化合并数据为第一个文件
    merged_data = base_file_info['df'].copy()
    merged_data['数据源'] = base_file_info['file']

    print(f"\n开始合并小区级别文件...")

    # 确定基础文件的字段类型
    base_field_type = get_cell_field_type(merged_data)
    if not base_field_type:
        raise ValueError("基础文件没有找到任何小区级别字段")

    print(f"基础文件字段类型: {base_field_type}")
    processed_field_types = [base_field_type]

    # 计算预期的合并键数量：基础字段数量 + 1个小区字段
    expected_key_count = len(BASE_FIELDS) + 1

    # 合并其他文件
    for i, file_info in enumerate(cell_level_files):
        if file_info['file'] == base_file_info['file']:
            continue  # 跳过基础文件

        df = file_info['df'].copy()
        df['数据源'] = file_info['file']

        # 确定当前文件的字段类型
        current_field_type = get_cell_field_type(df)
        if not current_field_type:
            print(f"跳过文件: {os.path.basename(file_info['file'])[:40]}... (无小区字段)")
            continue

        print(f"\n合并文件 {i+1}: {os.path.basename(file_info['file'])[:40]}...")
        print(f"当前文件字段类型: {current_field_type}")

        # 准备合并键
        left_on, right_on = prepare_merge_keys(merged_data, df, base_field_type, current_field_type)

        # 检查合并键是否足够
        if len(left_on) < expected_key_count or len(right_on) < expected_key_count:
            print(f"跳过文件: 合并键不足 ({len(left_on)}, {len(right_on)}), 预期至少 {expected_key_count} 个")
            continue

        print(f"  左合并键: {left_on}")
        print(f"  右合并键: {right_on}")

        try:
            # 执行合并
            merged_data, success = merge_single_file_data(merged_data, df, left_on, right_on, i)

            # 如果是新的字段类型，添加到已处理列表
            if success and current_field_type not in processed_field_types:
                processed_field_types.append(current_field_type)

        except Exception as e:
            print(f"  合并失败: {str(e)}")

    print(f"\n所有文件合并完成")
    print(f"最终数据: {len(merged_data)} 行, {len(merged_data.columns)} 列")
    print(f"处理的字段类型: {processed_field_types}")

    return merged_data

def save_merged_data(merged_data, output_file="KPI合并结果.xlsx"):
    """保存合并后的数据"""
    print(f"\n保存结果到: {output_file}")

    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        merged_data.to_excel(writer, sheet_name='KPI合并数据', index=False)

    print(f"数据已保存到: {output_file}")



def print_final_summary(merged_data):
    """打印最终摘要信息"""
    print("\n" + "="*50)
    print("🎉 KPI数据合并完成！")
    print(f"最终行数: {len(merged_data)}")
    print(f"最终列数: {len(merged_data.columns)}")

    # 数据源分布
    print(f"\n📊 数据源分布:")
    if '数据源' in merged_data.columns:
        source_counts = merged_data['数据源'].value_counts()
        for source, count in source_counts.items():
            print(f"  {os.path.basename(source)[:40]}... : {count} 行")

    # 合并特点
    print(f"\n✅ 合并特点:")
    print(f"  ✓ 智能识别小区级别数据")
    print(f"  ✓ 自动适应不同文件的字段差异")
    print(f"  ✓ 基于时间和小区ID进行列追加合并")
    print(f"  ✓ 排除了网元级别数据，避免业务逻辑错误")

def validate_merge_result(merged_data):
    """验证合并结果"""
    print(f"\n🔍 合并结果验证:")

    unique_cells = None
    unique_times = None

    # 检查是否有小区ID字段
    cell_field = None
    for field in CELL_FIELDS:
        if field in merged_data.columns:
            cell_field = field
            break
    
    # 检查是否有时间字段
    time_field = None
    for field in ['开始时间', 'Start Time', 'StartTime']:
        if field in merged_data.columns:
            time_field = field
            break

    if cell_field:
        unique_cells = merged_data[cell_field].nunique()
        print(f"  物理小区数量: {unique_cells}")

    if time_field:
        unique_times = merged_data[time_field].nunique()
        print(f"  时间段数量: {unique_times}")

        if unique_cells and unique_times:
            expected_rows = unique_cells * unique_times
            print(f"  理论行数: {unique_cells} × {unique_times} = {expected_rows}")

            if len(merged_data) == expected_rows:
                print(f"  ✅ 行数验证通过")
            else:
                print(f"  ⚠️  行数不匹配，可能有重复或缺失数据")

def main():
    """主函数 - 执行完整的KPI数据合并流程"""
    print("🎯 KPI数据合并工具")
    print("="*50)

    try:
        # 1. 查找Excel文件
        excel_files = find_excel_files()
        if not excel_files:
            print("❌ 没有找到Excel文件")
            return
            
        # 1.5 智能检测字段（中英文适配）
        try:
            # 先检测所有Excel文件中的字段
            detect_fields(excel_files)
        except ValueError as e:
            print(f"\n❌ {str(e)}")
            return

        # 2. 筛选有效文件（只保留小区级别文件）
        cell_level_files = filter_valid_files(excel_files)
        if not cell_level_files:
            print("❌ 没有找到符合条件的小区级别文件")
            return

        # 3. 直接使用第一个文件作为基础文件
        base_file_info = cell_level_files[0]
        base_file = base_file_info['file']
        print(f"\n使用第一个文件作为基础: {os.path.basename(base_file)[:50]}...")
        print(f"基础文件字段: {base_file_info['cell_fields']}")

        # 4. 使用递归合并逻辑
        merged_data = merge_all_files(cell_level_files, base_file_info)

        # 5. 保存结果
        save_merged_data(merged_data)

        # 6. 打印摘要信息
        print_final_summary(merged_data)
        validate_merge_result(merged_data)

        print("="*50)

    except Exception as e:
        print(f"❌ 合并过程中出现错误: {str(e)}")
        raise

if __name__ == "__main__":
    main()












