# KPI数据校验工具 v3.0 - 集成智能合并功能

## 概述

本工具集成了原有的KPI数据合并工具和kpi_validation.py的功能，提供了一个统一的解决方案，用于智能合并KPI文件并进行数据校验。

## 主要功能特性

### 1. 智能合并功能（来自KPI数据合并工具）
- **小区级别文件识别**：自动识别和筛选小区级别的KPI文件，排除网元级别文件
- **智能字段检测**：支持中英文字段映射，自动适配不同格式的字段名称
- **列追加合并**：基于时间段和小区ID进行列追加合并，实现约364行的合并结果
- **排除机制**：自动排除包含特定关键字的文件（波束信息ID、可替换单元ID）
- **多页面合并**：支持Sheet0和计数器定义页面的完整合并

### 2. 数据校验功能（来自kpi_validation.py）
- **灵活规则引擎**：支持复杂的条件表达式和计数器比较
- **完整数据保护**：保留所有非计数器字段和计数器字段
- **详细校验报告**：包含规则匹配状态和缺失计数器信息
- **智能规则筛选**：根据可用计数器自动筛选适用的校验规则

### 3. 集成优势
- **统一工作流**：一个脚本完成文件合并和数据校验
- **直接合并**：不分组，直接使用KPI数据合并工具的原始逻辑
- **智能适配**：自动适配不同小区字段类型的文件
- **严格合并**：KPI合并失败时直接报错，确保数据质量
- **完整性保证**：确保合并过程中数据的完整性和一致性

## 使用方法

### 基本用法
```bash
python kpi_validation_integrated.py <directory_path>
```

### 示例
```bash
# Windows路径
python kpi_validation_integrated.py "D:\Data\Excel Files"

# Linux/Mac路径
python kpi_validation_integrated.py /path/to/excel/files
```

## 工作流程

### 1. 文件扫描和筛选
- 扫描指定目录中的所有Excel文件
- 智能检测字段映射（中英文适配）
- 筛选有效的小区级别文件
- 排除网元级别文件和包含特定关键字的文件

### 2. 直接合并（不分组）
- 使用KPI数据合并工具的原始合并逻辑
- 以第一个有效文件作为基础文件
- 逐个合并其他文件，基于时间段和小区ID进行列追加合并
- 支持不同小区字段类型的文件合并
- 要求100%匹配率才进行合并
- 支持Sheet0、Sheet1和计数器定义页面合并

### 3. 数据校验
- 加载内置校验规则
- 根据可用计数器筛选适用规则
- 对每行数据进行规则验证
- 生成详细的校验结果报告

## 配置说明

### 字段映射配置
```python
FIELD_MAPPINGS = {
    'CELL_FIELDS': {
        'CU小区配置ID': ['CU小区配置ID'],
        'nrPhysicalCellDUId': ['nrPhysicalCellDUId'],
        'NRCarrierId': ['NRCarrierId']
    },
    'BASE_FIELDS': {
        '开始时间': ['开始时间', 'Start Time', 'StartTime'],
        '结束时间': ['结束时间', 'End Time', 'EndTime'],
        # ... 更多字段映射
    }
}
```

### 排除关键字配置
```python
EXCLUDE_KEYWORDS = ['波束信息ID', '可替换单元ID']
```

## 输出结果

### 1. 合并文件
- 文件名格式：`file1_file2_file3.xlsx`
- 包含Sheet0、Sheet1（如果存在）和计数器定义页面
- 数据源追踪：每行数据包含来源文件信息

### 2. 校验结果
每个处理后的文件会添加两个新的sheet页：
- **校验结果**：包含每行数据对每个规则的校验结果
- **Summary**：包含规则匹配状态和缺失计数器信息

## 校验规则

工具内置了8个校验规则，涵盖：
1. PDCP弃包率与下行padding率关系
2. 下行无丢包时的时延指标
3. 上行PDCP弃包率与padding率关系
4. 上行无丢包时的时延指标
5. 上行BLER和HARQ失败比例
6. 下行BLER和HARQ失败比例
7. 上行干扰电平
8. 随机接入和连接建立成功率

## 测试功能

提供了测试脚本 `test_kpi_integration.py` 用于验证各个功能模块：

```bash
python test_kpi_integration.py
```

测试内容包括：
- 文件扫描功能
- 字段检测功能
- 文件筛选功能
- 文件分组功能
- 文件处理功能
- 合并功能
- 单文件校验功能

## 注意事项

1. **文件格式要求**：
   - 支持.xlsx、.xls、.xlsm格式
   - Sheet0包含主要数据
   - 计数器定义在"指标(计数器)"或"KPI(Counter)"页面

2. **合并条件**：
   - 文件必须包含小区级别字段
   - 不能包含排除关键字
   - 需要100%的合并键匹配率

3. **校验条件**：
   - 规则中的计数器必须在数据中存在
   - 支持复杂的条件表达式
   - 必要条件和判决条件分别评估

## 错误处理

- **字段检测失败**：抛出异常，停止处理
- **合并失败**：抛出异常，停止处理（确保数据质量）
- **校验失败**：跳过相应规则，继续其他规则
- **文件读取失败**：跳过问题文件，继续处理其他文件

## 版本历史

- **v3.0**：集成KPI数据合并工具功能，支持Sheet1合并
- **v2.0**：支持基于记录内容的智能文件合并
- **v1.0**：基础校验功能

## 技术支持

如有问题或建议，请联系开发团队。
