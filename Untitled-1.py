#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pandas Merge 函数演示
展示不同合并方式和列交集方法的效果
"""

import pandas as pd
import numpy as np

def create_demo_data():
    """创建演示数据"""
    # 创建第一个数据框 - CU数据
    df1 = pd.DataFrame({
        '开始时间': ['2023-05-06 14:00', '2023-05-06 14:00', '2023-05-06 14:05'],
        '结束时间': ['2023-05-06 14:05', '2023-05-06 14:05', '2023-05-06 14:10'],
        '粒度': ['5分钟', '5分钟', '5分钟'],
        '子网ID': ['SN001', 'SN001', 'SN001'],
        'CU小区配置ID': ['Cell1', 'Cell2', 'Cell1'],
        'KPI_上行PRB利用率': [45.2, 38.7, 42.1],
        'KPI_下行PRB利用率': [78.3, 82.1, 75.6]
    })
    
    # 创建第二个数据框 - DU数据
    df2 = pd.DataFrame({
        '开始时间': ['2023-05-06 14:00', '2023-05-06 14:00', '2023-05-06 14:05'],
        '结束时间': ['2023-05-06 14:05', '2023-05-06 14:05', '2023-05-06 14:10'],
        '粒度': ['5分钟', '5分钟', '5分钟'],
        '子网ID': ['SN001', 'SN001', 'SN001'],
        'DU物理小区ID': ['Cell1', 'Cell2', 'Cell1'],
        'KPI_RRC连接建立成功率': [99.1, 98.5, 99.3],
        'KPI_无线掉线率': [0.12, 0.18, 0.11]
    })
    
    print("=== 演示数据 ===")
    print("\nCU数据 (df1):")
    print(df1)
    print("\nDU数据 (df2):")
    print(df2)
    
    return df1, df2

def demo_basic_merge(df1, df2):
    """演示基本的合并方法"""
    print("\n=== 1. 基本合并方法 ===")
    
    # 1.1 使用共同列名合并
    print("\n1.1 使用共同列名合并 (on=['开始时间', '结束时间', '粒度','子网ID'])")
    result = pd.merge(df1, df2, on='开始时间')
    print(f"结果形状: {result.shape}")
    print(result)
    
    # 1.2 使用不同列名合并
    print("\n1.2 使用不同列名合并 (left_on='CU小区配置ID', right_on='DU物理小区ID')")
    result = pd.merge(df1, df2, left_on='CU小区配置ID', right_on='DU物理小区ID')
    print(f"结果形状: {result.shape}")
    print(result)
    
    # 1.3 使用多列合并
    print("\n1.3 使用多列合并 (on=['开始时间', '结束时间', '粒度'])")
    result = pd.merge(df1, df2, on=['开始时间', '结束时间', '粒度'])
    print(f"结果形状: {result.shape}")
    print(result)

def demo_merge_types(df1, df2):
    """演示不同的合并类型"""
    print("\n=== 2. 不同合并类型 ===")
    
    # 准备带有缺失数据的测试数据
    df1_test = df1.copy()
    df2_test = df2.copy()
    
    # 添加一行只在df1中存在的数据
    df1_test = pd.concat([df1_test, pd.DataFrame({
        '开始时间': ['2023-05-06 14:10'],
        '结束时间': ['2023-05-06 14:15'],
        '粒度': ['5分钟'],
        '子网ID': ['SN001'],
        'CU小区配置ID': ['Cell3'],
        'KPI_上行PRB利用率': [50.1],
        'KPI_下行PRB利用率': [80.5]
    })], ignore_index=True)
    
    # 添加一行只在df2中存在的数据
    df2_test = pd.concat([df2_test, pd.DataFrame({
        '开始时间': ['2023-05-06 14:15'],
        '结束时间': ['2023-05-06 14:20'],
        '粒度': ['5分钟'],
        '子网ID': ['SN001'],
        'DU物理小区ID': ['Cell4'],
        'KPI_RRC连接建立成功率': [97.8],
        'KPI_无线掉线率': [0.22]
    })], ignore_index=True)
    
    # 定义合并键
    merge_keys = ['开始时间', '结束时间', '粒度']
    
    # 2.1 内连接 (inner)
    print("\n2.1 内连接 (how='inner')")
    result = pd.merge(df1_test, df2_test, on=merge_keys, how='inner')
    print(f"结果形状: {result.shape}")
    print(result)
    
    # 2.2 左连接 (left)
    print("\n2.2 左连接 (how='left')")
    result = pd.merge(df1_test, df2_test, on=merge_keys, how='left')
    print(f"结果形状: {result.shape}")
    print(result)
    
    # 2.3 右连接 (right)
    print("\n2.3 右连接 (how='right')")
    result = pd.merge(df1_test, df2_test, on=merge_keys, how='right')
    print(f"结果形状: {result.shape}")
    print(result)
    
    # 2.4 外连接 (outer)
    print("\n2.4 外连接 (how='outer')")
    result = pd.merge(df1_test, df2_test, on=merge_keys, how='outer')
    print(f"结果形状: {result.shape}")
    print(result)

def demo_suffix_handling(df1, df2):
    """演示后缀处理"""
    print("\n=== 3. 后缀处理 ===")
    
    # 3.1 默认后缀
    print("\n3.1 默认后缀 (suffixes=('_x', '_y'))")
    result = pd.merge(df1, df2, on=['开始时间', '结束时间', '粒度', '子网ID'])
    print(f"结果形状: {result.shape}")
    print(result)
    
    # 3.2 自定义后缀
    print("\n3.2 自定义后缀 (suffixes=('_CU', '_DU'))")
    result = pd.merge(df1, df2, on=['开始时间', '结束时间', '粒度', '子网ID'], suffixes=('_CU', '_DU'))
    print(f"结果形状: {result.shape}")
    print(result)
    
    # 3.3 空后缀
    print("\n3.3 一个空后缀 (suffixes=('', '_DU'))")
    result = pd.merge(df1, df2, on=['开始时间', '结束时间', '粒度', '子网ID'], suffixes=('', '_DU'))
    print(f"结果形状: {result.shape}")
    print(result)

def demo_intersection_merge(df1, df2):
    """演示列交集合并方法"""
    print("\n=== 4. 列交集合并方法 ===")
    
    # 4.1 计算列交集
    intersection_cols = set(df1.columns) & set(df2.columns)
    print(f"\n4.1 列交集: {intersection_cols}")
    
    # 4.2 使用列交集作为合并键
    print("\n4.2 使用列交集作为合并键")
    base_merge_cols = list(intersection_cols)
    
    # 添加小区字段
    left_on = base_merge_cols.copy()
    right_on = base_merge_cols.copy()
    left_on.append('CU小区配置ID')
    right_on.append('DU物理小区ID')
    
    print(f"左表合并键: {left_on}")
    print(f"右表合并键: {right_on}")
    
    result = pd.merge(
        df1, 
        df2,
        left_on=left_on,
        right_on=right_on,
        suffixes=('', '_追加')
    )
    
    print(f"结果形状: {result.shape}")
    print(result)
    
    # 4.3 清理重复列
    print("\n4.3 清理重复列")
    # 找出所有带后缀的列
    suffix_columns = [col for col in result.columns if col.endswith('_追加')]
    
    # 找出对应的原始列
    original_columns = []
    for col in suffix_columns:
        original_col = col[:-len('_追加')]
        if original_col in result.columns:
            original_columns.append(col)
    
    # 删除重复列
    result_cleaned = result.drop(columns=original_columns)
    
    print(f"清理前列数: {len(result.columns)}")
    print(f"清理后列数: {len(result_cleaned.columns)}")
    print(f"删除的列: {original_columns}")
    print(result_cleaned)

def main():
    """主函数"""
    print("🔍 Pandas Merge 函数演示")
    print("="*50)
    
    # 创建演示数据
    df1, df2 = create_demo_data()
    
    # 演示基本合并方法
    demo_basic_merge(df1, df2)
    
    # 演示不同合并类型
    demo_merge_types(df1, df2)
    
    # 演示后缀处理
    demo_suffix_handling(df1, df2)
    
    # 演示列交集合并方法
    demo_intersection_merge(df1, df2)
    
    print("\n="*50)
    print("演示完成！")

if __name__ == "__main__":
    main()