#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
KPI字段检测调试脚本

用于调试KPI字段检测问题，分析文件中实际包含的字段
"""

import os
import pandas as pd
import glob

# 字段映射配置
FIELD_MAPPINGS = {
    'CELL_FIELDS': {
        'CU小区配置ID': ['CU小区配置ID'],
        'nrPhysicalCellDUId': ['nrPhysicalCellDUId'],
        'NRCarrierId': ['NRCarrierId']
    },
    'BASE_FIELDS': {
        '开始时间': ['开始时间', 'Start Time', 'StartTime'],
        '结束时间': ['结束时间', 'End Time', 'EndTime'],
        '粒度': ['粒度', 'Granularity Period', 'GranularityPeriod'],
        'SubnetWork ID': ['SubnetWork ID', 'SubnetworkId', '子网ID', 'Subnetwork ID'],
        'SubnetWork Name': ['SubnetWork Name', 'SubnetworkName', '子网名称', 'Subnetwork Name'],
        'ManagedElement ID': ['ManagedElement ID', 'ManagedElementId', '网元ID', 'Managed Element ID'],
        '管理网元': ['管理网元']
    }
}

def scan_excel_files(directory_path="."):
    """扫描Excel文件"""
    excel_extensions = ['*.xlsx', '*.xls', '*.xlsm']
    excel_files = []

    for extension in excel_extensions:
        pattern = os.path.join(directory_path, extension)
        files = glob.glob(pattern)
        excel_files.extend(files)

    excel_files = sorted(list(set(excel_files)))
    return excel_files

def analyze_file_fields(file_path):
    """分析单个文件的字段"""
    print(f"\n分析文件: {os.path.basename(file_path)}")
    print("-" * 60)
    
    try:
        df = pd.read_excel(file_path, sheet_name=0)
        print(f"行数: {len(df)}")
        print(f"列数: {len(df.columns)}")
        
        print(f"\n所有列名:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i:2d}. {col}")
        
        # 检查小区字段
        print(f"\n小区字段检查:")
        found_cell_fields = []
        for field_key, field_variants in FIELD_MAPPINGS['CELL_FIELDS'].items():
            print(f"  检查 {field_key}:")
            for variant in field_variants:
                if variant in df.columns:
                    print(f"    ✓ 找到: {variant}")
                    found_cell_fields.append(variant)
                else:
                    print(f"    ✗ 未找到: {variant}")
        
        if found_cell_fields:
            print(f"\n✅ 找到的小区字段: {found_cell_fields}")
        else:
            print(f"\n❌ 未找到任何小区字段")
        
        # 检查基础字段
        print(f"\n基础字段检查:")
        found_base_fields = []
        for field_key, field_variants in FIELD_MAPPINGS['BASE_FIELDS'].items():
            print(f"  检查 {field_key}:")
            for variant in field_variants:
                if variant in df.columns:
                    print(f"    ✓ 找到: {variant}")
                    found_base_fields.append(variant)
                    break
            else:
                print(f"    ✗ 未找到任何变体")
        
        print(f"\n找到的基础字段: {found_base_fields}")
        
        return {
            'file': file_path,
            'rows': len(df),
            'columns': len(df.columns),
            'cell_fields': found_cell_fields,
            'base_fields': found_base_fields,
            'all_columns': list(df.columns)
        }
        
    except Exception as e:
        print(f"❌ 读取文件失败: {str(e)}")
        return None

def analyze_all_files():
    """分析所有文件"""
    print("KPI字段检测调试")
    print("=" * 60)
    
    current_dir = os.getcwd()
    print(f"当前目录: {current_dir}")
    
    excel_files = scan_excel_files()
    print(f"\n找到 {len(excel_files)} 个Excel文件:")
    for i, file in enumerate(excel_files, 1):
        print(f"  {i}. {os.path.basename(file)}")
    
    if not excel_files:
        print("没有找到Excel文件")
        return
    
    # 分析每个文件
    file_analysis = []
    for file in excel_files:
        analysis = analyze_file_fields(file)
        if analysis:
            file_analysis.append(analysis)
    
    # 汇总分析
    print(f"\n" + "=" * 60)
    print("汇总分析")
    print("=" * 60)
    
    all_columns = set()
    all_cell_fields = set()
    all_base_fields = set()
    
    for analysis in file_analysis:
        all_columns.update(analysis['all_columns'])
        all_cell_fields.update(analysis['cell_fields'])
        all_base_fields.update(analysis['base_fields'])
    
    print(f"\n所有文件中出现的列名 ({len(all_columns)} 个):")
    for col in sorted(all_columns):
        print(f"  - {col}")
    
    print(f"\n所有文件中找到的小区字段 ({len(all_cell_fields)} 个):")
    for field in sorted(all_cell_fields):
        print(f"  - {field}")
    
    print(f"\n所有文件中找到的基础字段 ({len(all_base_fields)} 个):")
    for field in sorted(all_base_fields):
        print(f"  - {field}")
    
    # 检查字段覆盖情况
    print(f"\n字段覆盖情况:")
    print(f"小区字段类型覆盖:")
    for field_key, field_variants in FIELD_MAPPINGS['CELL_FIELDS'].items():
        found = any(variant in all_columns for variant in field_variants)
        status = "✓" if found else "✗"
        print(f"  {status} {field_key}: {field_variants}")
    
    print(f"\n基础字段类型覆盖:")
    for field_key, field_variants in FIELD_MAPPINGS['BASE_FIELDS'].items():
        found = any(variant in all_columns for variant in field_variants)
        status = "✓" if found else "✗"
        print(f"  {status} {field_key}: {field_variants}")
    
    # 文件分类
    print(f"\n文件分类:")
    valid_files = []
    invalid_files = []
    
    for analysis in file_analysis:
        if analysis['cell_fields']:
            valid_files.append(analysis)
            print(f"  ✓ {os.path.basename(analysis['file'])} - 小区字段: {analysis['cell_fields']}")
        else:
            invalid_files.append(analysis)
            print(f"  ✗ {os.path.basename(analysis['file'])} - 无小区字段")
    
    print(f"\n总结:")
    print(f"  有效文件: {len(valid_files)} 个")
    print(f"  无效文件: {len(invalid_files)} 个")
    
    if valid_files:
        print(f"\n建议的字段配置:")
        suggested_cell_fields = list(all_cell_fields)
        suggested_base_fields = list(all_base_fields)
        print(f"  CELL_FIELDS = {suggested_cell_fields}")
        print(f"  BASE_FIELDS = {suggested_base_fields}")

if __name__ == "__main__":
    analyze_all_files()
