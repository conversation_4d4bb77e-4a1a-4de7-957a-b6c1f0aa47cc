#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
KPI数据校验工具 v3.0 - 集成智能合并功能

功能特性：
1. 集成KPI数据合并工具的智能合并功能
2. 支持小区级别文件的智能识别和筛选
3. 基于小区字段进行列追加合并（364行左右的合并结果）
4. 支持Sheet0、Sheet1和计数器定义页面的完整合并
5. 灵活的规则引擎：支持复杂的条件表达式和计数器比较
6. 完整的数据保护：保留所有非计数器字段和计数器字段
7. 详细的校验结果报告：包含规则匹配状态和缺失计数器信息

校验原则：
1) 必要条件必须满足才能进行判决条件评估
2) 判决条件按顺序评估，所有条件都必须满足才能通过
3) 支持表达式计算和复杂的逻辑判断

合并原则：
1) 智能识别小区级别数据，排除网元级别数据
2) 基于时间段和小区ID进行列追加合并
3) 排除包含特定关键字的文件（波束信息ID、可替换单元ID）
4) 支持中英文字段映射和智能字段检测

使用方法：
python kpi_validation.py <directory_path>

示例：
python kpi_validation.py /path/to/excel/files
python kpi_validation.py "D:\Data\Excel Files"

作者：Augment Agent
版本：3.0
更新：集成KPI数据合并工具功能，支持Sheet1合并
"""

import pandas as pd
import json
import re
import numpy as np
import sys
import os
import glob
from typing import List, Dict, Any, Tuple, Set

# ==================== 常量定义 ====================

# 支持的计数器表名称
COUNTER_SHEET_NAMES = ["指标(计数器)", "KPI(Counter)"]

# 计数器ID列识别关键词
COUNTER_ID_KEYWORDS = ["指标或计数器", "kpi or counter"]

# 校验结果状态
VALIDATION_STATUS = {
    'PASS': '通过',
    'FAIL': '失败',
    'SKIP': '跳过',
    'NOT_VALIDATED': '未验证'
}

# 总校验结果状态
OVERALL_STATUS = {
    'SUCCESS': '成功',
    'FAILURE': '失败',
    'SKIP': '跳过',
    'UNKNOWN': '未知'
}

# 不允许合并的表头字段（来自KPI合并工具）
NO_MERGE_HEADERS = ['可替换单元ID', 'subBeamIndex', '波束信息ID']

# KPI合并工具配置
EXCLUDE_KEYWORDS = ['波束信息ID', '可替换单元ID']
CELL_FIELDS = ['CU小区配置ID', 'nrPhysicalCellDUId', 'NRCarrierId']
BASE_FIELDS = ['开始时间', '结束时间', '粒度', 'SubnetWork ID', 'SubnetWork Name', 'ManagedElement ID', '管理网元']

# 字段映射（中英文对照）- 来自KPI合并工具
FIELD_MAPPINGS = {
    # 排除关键字映射
    'EXCLUDE_KEYWORDS': {
        '波束信息ID': ['波束信息ID'],
        '可替换单元ID': ['可替换单元ID', 'ReplaceableUnitId', 'Replaceable Unit ID']
    },

    # 小区字段映射
    'CELL_FIELDS': {
        'CU小区配置ID': ['CU小区配置ID'],
        'nrPhysicalCellDUId': ['nrPhysicalCellDUId'],
        'NRCarrierId': ['NRCarrierId']
    },

    # 基础字段映射
    'BASE_FIELDS': {
        '开始时间': ['开始时间', 'Start Time', 'StartTime'],
        '结束时间': ['结束时间', 'End Time', 'EndTime'],
        '粒度': ['粒度', 'Granularity Period', 'GranularityPeriod'],
        'SubnetWork ID': ['SubnetWork ID', 'SubnetworkId', '子网ID', 'Subnetwork ID'],
        'SubnetWork Name': ['SubnetWork Name', 'SubnetworkName', '子网名称', 'Subnetwork Name'],
        'ManagedElement ID': ['ManagedElement ID', 'ManagedElementId', '网元ID', 'Managed Element ID'],
        '管理网元': ['管理网元']
    }
}

# 内置校验规则
VALIDATION_RULES = {
    "rules": [
        {
            "id": 1,
            "counters": [
                "668166 小区下行PDCP层平均流量(Mbps)",
                "1711020040323 下行MAC层流量-高频_1711073545754",
                "668002 小区下行 PDCP SDU弃包率(%)"
            ],
            "necessary_conditions": [
                {"counter": "668166", "operator": ">", "value": 0},
                {"counter": "1711020040323", "operator": ">", "value": 0}
            ],
            "judgment_conditions": [
                {"counter": "668002", "operator": "=", "value": 0},
                {"expression": "(1711020040323-668166)/668166", "operator": ">=", "value": 0},
                {"expression": "(1711020040323-668166)/668166", "operator": "<", "value": 25}
            ],
            "description": "PDCP弃包率为0%时，下行padding率大于0%且小于25%"
        },
        {
            "id": 2,
            "counters": [
                "668166 小区下行PDCP层平均流量(Mbps)",
                "668002 小区下行 PDCP SDU弃包率(%)",
                "668001 小区下行CUUP平均时延(ms)",
                "669503 小区下行RLC SDU平均时延(ms)",
                "669504 小区下行Ip平均时延(ms)"
            ],
            "necessary_conditions": [
                {"counter": "668166", "operator": ">", "value": 0},
                {"counter": "668002", "operator": "=", "value": 0}
            ],
            "judgment_conditions": [
                {"counter": "668001", "operator": "<", "value": 5},
                {"expression": "669503 < 669504", "operator": "=", "value": True},
                {"counter": "669503", "operator": "<", "value": 20}
            ],
            "description": "下行无丢包时，CUUP在5ms以内，IP时延小于RLC SDU，RLC SDU通常在20ms以内"
        },
        {
            "id": 3,
            "counters": [
                "668167 小区上行PDCP层平均流量(Mbps)",
                "1711020040324 上行MAC层流量-高频_1711073545945",
                "668000 小区上行PDCP SDU丢包率(%)"
            ],
            "necessary_conditions": [
                {"counter": "668167", "operator": ">", "value": 0},
                {"counter": "1711020040324", "operator": ">", "value": 0}
            ],
            "judgment_conditions": [
                {"counter": "668000", "operator": "=", "value": 0},
                {"expression": "(1711020040324-668167)/668167", "operator": ">=", "value": 0},
                {"expression": "(1711020040324-668167)/668167", "operator": "<", "value": 200}
            ],
            "description": "PDCP弃包率为0%时，下行padding率大于0%且小于200%"
        },
        {
            "id": 4,
            "counters": [
                "668167 小区上行PDCP层平均流量(Mbps)",
                "668000 小区上行PDCP SDU丢包率(%)",
                "668018 小区上行CUUP平均时延(ms)",
                "669505 小区上行RLC SDU平均时延(ms)"
            ],
            "necessary_conditions": [
                {"counter": "668167", "operator": ">", "value": 0},
                {"counter": "668000", "operator": "=", "value": 0}
            ],
            "judgment_conditions": [
                {"counter": "668018", "operator": "<", "value": 5},
                {"counter": "669505", "operator": "<", "value": 20}
            ],
            "description": "上行无丢包时，CUUP在5ms以内，RLC SDU通常在20ms以内"
        },
        {
            "id": 5,
            "counters": [
                "668167 小区上行PDCP层平均流量(Mbps)",
                "670138 小区上行BLER(%)",
                "670033 小区上行HARQ失败比例(%)"
            ],
            "necessary_conditions": [
                {"counter": "668167", "operator": ">", "value": 0}
            ],
            "judgment_conditions": [
                {"counter": "670138", "operator": "<=", "value": 10},
                {"counter": "670033", "operator": "<=", "value": 0.5}
            ],
            "description": "目标bler10%，harq fail比例控制在千分之5以内"
        },
        {
            "id": 6,
            "counters": [
                "668166 小区下行PDCP层平均流量(Mbps)",
                "670139 小区下行BLER(%)",
                "670034 小区下行HARQ失败比例(%)"
            ],
            "necessary_conditions": [
                {"counter": "668166", "operator": ">", "value": 0}
            ],
            "judgment_conditions": [
                {"counter": "670139", "operator": "<=", "value": 10},
                {"counter": "670034", "operator": "<=", "value": 0.5}
            ],
            "description": "目标bler10%，harq fail比例控制在千分之5以内"
        },
        {
            "id": 7,
            "counters": [
                "668167 小区上行PDCP层平均流量(Mbps)",
                "C616650043 小区上行干扰电平(dBm)",
                "C616650045 小区上行干扰电平(dBm)"
            ],
            "necessary_conditions": [
                {"counter": "668167", "operator": ">", "value": 0}
            ],
            "judgment_conditions": [
                {"counter": "C616650043", "operator": "<", "value": -100},
                {"counter": "C616650045", "operator": "<", "value": -100}
            ],
            "description": "小区上行干扰电平需低于-100dBm"
        },
        {
            "id": 8,
            "counters": [
                "C616310004 小区随机接入次数",
                "C616310006 小区随机接入次数",
                "667184 小区随机接入成功率(%)",
                "667000 小区RRC连接建立成功率(%)",
                "667015 小区RRC连接重建成功率(%)"
            ],
            "necessary_conditions": [
                {"expression": "C616310004 > 0 or C616310006 > 0", "operator": "=", "value": True}
            ],
            "judgment_conditions": [
                {"counter": "667184", "operator": ">", "value": 99.9},
                {"counter": "667000", "operator": ">", "value": 99.9},
                {"counter": "667015", "operator": ">", "value": 99.9}
            ],
            "description": "有msg1统计的环境，接通率等成功率指标必须大于99.9%"
        }
    ]
}

# ==================== KPI合并工具函数 ====================

def is_valid_kpi_file(file_path):
    """检查文件是否为有效的KPI文件（来自KPI合并工具）"""
    try:
        df = pd.read_excel(file_path, sheet_name=0)

        # 检查排除关键字
        columns_str = ' '.join(df.columns.astype(str))
        has_exclude = any(keyword in columns_str for keyword in EXCLUDE_KEYWORDS)

        # 检查是否有数据
        has_data = len(df) > 0

        # 检查是否有小区级别字段
        has_cell_fields = any(field in df.columns for field in CELL_FIELDS)

        if has_exclude:
            return False, "包含排除关键字"
        elif not has_data:
            return False, "无数据行"
        elif not has_cell_fields:
            return False, "网元级别(无小区字段)"
        else:
            return True, "有效文件"

    except Exception as e:
        return False, f"读取错误: {str(e)}"


def detect_kpi_fields(excel_files):
    """智能检测所有文件的字段，更新全局字段变量（来自KPI合并工具）"""
    global EXCLUDE_KEYWORDS, CELL_FIELDS, BASE_FIELDS

    print("\n智能检测KPI字段...")

    # 收集所有文件的列名
    all_columns = set()
    for file in excel_files:
        try:
            df = pd.read_excel(file, sheet_name=0)
            # 确保列名被正确添加为字符串
            for col in df.columns:
                all_columns.add(str(col).strip())
        except Exception as e:
            print(f"  读取文件 {file} 失败: {str(e)}")

    # 检测并更新每种字段类型
    for field_type, mapping in FIELD_MAPPINGS.items():
        detected_fields = []

        for field_key, field_variants in mapping.items():
            found = False
            found_variant = None

            for variant in field_variants:
                variant_str = str(variant).strip()
                if variant_str in all_columns:
                    found_variant = variant_str
                    found = True
                    break

            if found:
                detected_fields.append(found_variant)
            else:
                # 对于排除关键字，如果没找到就不添加
                if field_type == 'EXCLUDE_KEYWORDS':
                    detected_fields.append(field_key)
                # 对于小区字段，如果没找到就报错
                elif field_type == 'CELL_FIELDS':
                    error_msg = f"错误: 未找到小区字段 {field_key} 的任何变体。请确保文件中包含以下字段之一: {field_variants}"
                    print(f"  {error_msg}")
                    raise ValueError(error_msg)
                else:
                    # 基础字段，如果没找到就保留原始字段名
                    detected_fields.append(field_key)

        # 更新全局变量
        if field_type == 'EXCLUDE_KEYWORDS':
            EXCLUDE_KEYWORDS = detected_fields
        elif field_type == 'CELL_FIELDS':
            if detected_fields:
                CELL_FIELDS = detected_fields
            else:
                error_msg = "错误: 未检测到任何小区字段，无法进行合并"
                print(f"  {error_msg}")
                raise ValueError(error_msg)
        elif field_type == 'BASE_FIELDS':
            if len(detected_fields) == len(mapping):
                BASE_FIELDS = detected_fields

    return {
        'EXCLUDE_KEYWORDS': EXCLUDE_KEYWORDS,
        'CELL_FIELDS': CELL_FIELDS,
        'BASE_FIELDS': BASE_FIELDS
    }


def filter_valid_kpi_files(excel_files):
    """筛选有效的KPI文件（只保留小区级别文件）"""
    valid_files = []

    print("\n检查KPI文件有效性...")
    for file in excel_files:
        is_valid, reason = is_valid_kpi_file(file)

        if is_valid:
            # 读取文件，获取小区字段和行数信息
            df = pd.read_excel(file, sheet_name=0)
            cell_fields = [field for field in CELL_FIELDS if field in df.columns]

            valid_files.append({
                'file': file,
                'df': df,
                'cell_fields': cell_fields,
                'rows': len(df)
            })
            print(f"✓ {os.path.basename(file)[:50]}... ({len(df)} 行, 字段: {cell_fields})")
        else:
            print(f"✗ {os.path.basename(file)[:50]}... ({reason})")

    print(f"\n小区级别文件: {len(valid_files)} 个")
    print(f"排除的文件: {len(excel_files) - len(valid_files)} 个")

    return valid_files


# ==================== 工具函数 ====================


def scan_excel_files(directory_path: str) -> List[str]:
    """
    扫描指定目录中的所有Excel文件

    Args:
        directory_path: 目录绝对路径

    Returns:
        List[str]: Excel文件路径列表
    """
    if not os.path.exists(directory_path):
        raise FileNotFoundError(f"Directory not found: {directory_path}")

    if not os.path.isdir(directory_path):
        raise NotADirectoryError(f"Path is not a directory: {directory_path}")

    # 支持的Excel文件扩展名
    excel_extensions = ['*.xlsx', '*.xls', '*.xlsm']
    excel_files = []

    for extension in excel_extensions:
        pattern = os.path.join(directory_path, extension)
        files = glob.glob(pattern)
        excel_files.extend(files)

    # 去重并排序
    excel_files = sorted(list(set(excel_files)))

    print(f"Found {len(excel_files)} Excel files in {directory_path}")

    return excel_files


def has_no_merge_headers(df: pd.DataFrame) -> bool:
    """
    检查DataFrame是否包含不允许合并的表头字段

    Args:
        df: 数据框

    Returns:
        bool: 如果包含不允许合并的表头返回True，否则返回False
    """
    columns = [str(col) for col in df.columns]
    for header in NO_MERGE_HEADERS:
        if header in columns:
            return True
    return False


def is_counter_column(column_name: str) -> bool:
    """
    判断列是否为计数器字段

    Args:
        column_name: 列名

    Returns:
        bool: 如果是计数器字段返回True，否则返回False

    计数器字段的特征：
    - 包含冒号(:)分隔符
    - 格式为 "计数器ID:描述" 或 "计数器ID[索引]:描述"
    """
    return ':' in str(column_name)


def extract_non_counter_columns(df: pd.DataFrame) -> List[str]:
    """
    提取非计数器字段列名

    Args:
        df: 数据框

    Returns:
        List[str]: 排序后的非计数器字段列名列表
    """
    return sorted([col for col in df.columns if not is_counter_column(col)])


def create_non_counter_signature(df: pd.DataFrame, non_counter_columns: List[str]) -> str:
    """
    创建非计数器字段记录内容的唯一签名

    此函数用于判断不同文件的非计数器记录内容是否相同，
    相同签名的文件可以进行合并操作。

    Args:
        df: 数据框
        non_counter_columns: 非计数器字段列名列表

    Returns:
        str: 记录内容的哈希签名

    处理步骤：
    1. 提取非计数器字段数据
    2. 填充NaN值为空字符串
    3. 按字段排序确保一致性
    4. 转换为字符串并计算哈希值
    """
    if not non_counter_columns:
        return "empty"

    try:
        # 提取非计数器字段数据
        non_counter_data = df[non_counter_columns].copy()

        # 处理可能的NaN值，统一为空字符串
        non_counter_data = non_counter_data.fillna('')

        # 按非计数器字段排序以确保不同文件间的一致性
        non_counter_data = non_counter_data.sort_values(by=non_counter_columns).reset_index(drop=True)

        # 转换为字符串表示并创建哈希签名
        records_str = non_counter_data.to_string(index=False, header=False)
        return str(hash(records_str))
    except Exception as e:
        return f"error_{len(df)}"


def group_files_for_kpi_merge(excel_files: List[str]) -> List[List[str]]:
    """
    KPI文件智能分组：基于小区级别数据进行分组

    使用KPI合并工具的逻辑，优先识别小区级别文件并进行智能分组

    Args:
        excel_files: Excel文件路径列表

    Returns:
        List[List[str]]: 文件分组列表，每个分组包含可以合并的文件路径

    分组策略：
    1. 首先智能检测字段映射
    2. 筛选出有效的小区级别文件
    3. 基于小区字段类型进行分组
    4. 排除包含特定关键字的文件
    """
    print(f"\nKPI智能分组 {len(excel_files)} 个文件...")

    try:
        # 1. 智能检测字段（中英文适配）
        detect_kpi_fields(excel_files)

        # 2. 筛选有效的小区级别文件
        valid_kpi_files = filter_valid_kpi_files(excel_files)

        if not valid_kpi_files:
            print("❌ 没有找到符合条件的小区级别文件")
            return []

        # 3. 按小区字段类型分组
        groups = {}
        for file_info in valid_kpi_files:
            # 获取文件的小区字段类型
            cell_field_type = None
            for field in CELL_FIELDS:
                if field in file_info['df'].columns:
                    cell_field_type = field
                    break

            if cell_field_type:
                if cell_field_type not in groups:
                    groups[cell_field_type] = []
                groups[cell_field_type].append(file_info['file'])

        # 4. 转换为分组列表
        grouped_files = list(groups.values())

        print(f"KPI分组结果: {len(grouped_files)} 个分组")
        for i, group in enumerate(grouped_files):
            print(f"  分组 {i+1}: {len(group)} 个文件")
            for file in group:
                print(f"    - {os.path.basename(file)}")

        return grouped_files

    except ValueError as e:
        print(f"❌ KPI分组失败: {str(e)}")
        # 降级到原始分组方法
        return group_files_for_merge_fallback(excel_files)


def group_files_for_merge_fallback(excel_files: List[str]) -> List[List[str]]:
    """
    降级文件分组方法：基于非计数器字段内容完全匹配进行分组
    """
    print(f"\n使用降级分组方法处理 {len(excel_files)} 个文件...")

    file_info = []

    # 第一阶段：收集每个文件的基本信息和签名
    for file_path in excel_files:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"  Warning: File not found, skipping: {file_path}")
            continue

        # 读取Sheet0数据
        sheet0_df = pd.read_excel(file_path, sheet_name='Sheet0')

        # 检查Sheet0是否只有表头没有数据行
        if sheet0_df.empty:
            print(f"  Info: {os.path.basename(file_path)} has headers only, will be processed individually")
            # 对于只有表头的文件，使用特殊签名确保单独分组且跳过后续处理
            unique_signature = f"headers_only_{file_path}"
            signature = ("headers_only", unique_signature)

            file_info.append({
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'row_count': 0,
                'non_counter_columns': (),
                'counter_columns_count': 0,
                'records_signature': "headers_only",
                'has_no_merge_headers': False,
                'headers_only': True,
                'signature': signature
            })
            continue

        # 检查是否包含不允许合并的表头
        has_no_merge = has_no_merge_headers(sheet0_df)

        # 提取非计数器字段
        non_counter_columns = extract_non_counter_columns(sheet0_df)

        # 创建非计数器记录内容的唯一签名
        records_signature = create_non_counter_signature(sheet0_df, non_counter_columns)

        # 统计计数器字段数量
        counter_columns_count = len([col for col in sheet0_df.columns if is_counter_column(col)])

        # 如果包含不允许合并的表头，使用特殊签名确保单独分组
        if has_no_merge:
            # 使用文件路径作为唯一签名，确保每个文件都单独分组
            unique_signature = f"no_merge_{file_path}"
            signature = (tuple(non_counter_columns), unique_signature)
        else:
            signature = (tuple(non_counter_columns), records_signature)

        file_info.append({
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'row_count': len(sheet0_df),
            'non_counter_columns': tuple(non_counter_columns),
            'counter_columns_count': counter_columns_count,
            'records_signature': records_signature,
            'has_no_merge_headers': has_no_merge,
            'headers_only': False,
            'signature': signature  # 组合签名
        })

    if not file_info:
        print("  No valid files found")
        return []

    # 第二阶段：按签名分组
    groups = {}
    for info in file_info:
        signature = info['signature']
        if signature not in groups:
            groups[signature] = []
        groups[signature].append(info['file_path'])

    # 第三阶段：显示分组结果
    grouped_files = list(groups.values())

    mergeable_groups = 0
    single_files = 0
    no_merge_files = 0
    headers_only_files = 0

    for i, group in enumerate(grouped_files):
        if len(group) > 1:
            mergeable_groups += 1
        else:
            single_file = group[0]
            # 检查文件类型
            file_info_item = next((info for info in file_info if info['file_path'] == single_file), None)
            if file_info_item:
                if file_info_item.get('headers_only', False):
                    headers_only_files += 1
                elif file_info_item.get('has_no_merge_headers', False):
                    no_merge_files += 1
                else:
                    single_files += 1
            else:
                single_files += 1

    print(f"降级分组结果: {len(grouped_files)} groups ({mergeable_groups} mergeable, {single_files} single, {no_merge_files} no-merge, {headers_only_files} headers-only)")
    return grouped_files


# ==================== KPI合并核心函数 ====================

def create_kpi_merge_key(df, fields):
    """创建KPI合并键（来自KPI合并工具）"""
    available_fields = [field for field in fields if field in df.columns]
    if available_fields:
        return df[available_fields].astype(str).apply(lambda x: '|'.join(x.values), axis=1)
    return None


def calculate_kpi_match_rate(left_keys, right_keys):
    """计算KPI匹配率（来自KPI合并工具）"""
    if len(left_keys) == 0:
        return 0
    return len(set(left_keys) & set(right_keys)) / len(set(left_keys)) * 100


def get_kpi_cell_field_type(df):
    """获取数据框的小区字段类型（来自KPI合并工具）"""
    for field in CELL_FIELDS:
        if field in df.columns:
            return field
    return None


def clean_kpi_duplicate_columns(merged_data, suffix_pattern='_追加'):
    """清理KPI合并中的重复列（来自KPI合并工具）"""
    # 找出所有包含后缀的列，但排除数据源列
    columns_to_drop = [col for col in merged_data.columns
                      if suffix_pattern in col and not col.startswith('数据源')]

    # 如果有需要删除的列，就删除
    if columns_to_drop:
        print(f"  删除的列: {columns_to_drop}")
        merged_data = merged_data.drop(columns=columns_to_drop)
        print(f"  清理了 {len(columns_to_drop)} 个带有'{suffix_pattern}'后缀的列")

    # 返回清理后的数据和删除的列数
    return merged_data, len(columns_to_drop)


def merge_kpi_files_advanced(excel_files: List[str], output_file: str) -> str:
    """
    使用KPI合并工具的高级合并逻辑

    Args:
        excel_files: 要合并的Excel文件路径列表
        output_file: 输出文件路径

    Returns:
        str: 合并后的文件路径
    """
    print(f"使用KPI高级合并逻辑合并 {len(excel_files)} 个文件...")

    try:
        # 1. 智能检测字段
        detect_kpi_fields(excel_files)

        # 2. 筛选有效文件
        valid_kpi_files = filter_valid_kpi_files(excel_files)

        if not valid_kpi_files:
            print("❌ 没有找到符合条件的小区级别文件")
            # 降级到原始合并方法
            return merge_excel_files(excel_files, output_file)

        # 3. 使用第一个文件作为基础文件
        base_file_info = valid_kpi_files[0]
        merged_data = base_file_info['df'].copy()
        merged_data['数据源'] = base_file_info['file']

        print(f"使用基础文件: {os.path.basename(base_file_info['file'])}")

        # 确定基础文件的字段类型
        base_field_type = get_kpi_cell_field_type(merged_data)
        if not base_field_type:
            print("❌ 基础文件没有找到任何小区级别字段")
            return merge_excel_files(excel_files, output_file)

        print(f"基础文件字段类型: {base_field_type}")

        # 4. 合并其他文件
        for i, file_info in enumerate(valid_kpi_files[1:], 1):
            df = file_info['df'].copy()
            df['数据源'] = file_info['file']

            # 确定当前文件的字段类型
            current_field_type = get_kpi_cell_field_type(df)
            if not current_field_type:
                print(f"跳过文件: {os.path.basename(file_info['file'])} (无小区字段)")
                continue

            print(f"\n合并文件 {i}: {os.path.basename(file_info['file'])}")
            print(f"当前文件字段类型: {current_field_type}")

            # 准备合并键
            left_on = BASE_FIELDS.copy()
            right_on = BASE_FIELDS.copy()

            # 如果字段类型相同，直接使用相同字段合并
            if current_field_type == base_field_type:
                left_on.append(current_field_type)
                right_on.append(current_field_type)
            else:
                # 不同字段类型，使用各自的字段
                left_on.append(base_field_type)
                right_on.append(current_field_type)

            print(f"  左合并键: {left_on}")
            print(f"  右合并键: {right_on}")

            # 检查匹配率
            left_keys = create_kpi_merge_key(merged_data[left_on].drop_duplicates(), left_on)
            right_keys = create_kpi_merge_key(df[right_on].drop_duplicates(), right_on)

            match_rate = calculate_kpi_match_rate(left_keys, right_keys)
            print(f"  合并键匹配率: {match_rate:.1f}%")

            if match_rate == 100:  # 要求100%匹配率
                before_cols = len(merged_data.columns)
                before_rows = len(merged_data)

                # 使用内连接合并
                merged_data = pd.merge(
                    merged_data,
                    df,
                    left_on=left_on,
                    right_on=right_on,
                    how='inner',
                    suffixes=('', f'_追加{i}')
                )

                # 清理重复的基础字段和小区字段
                merged_data, _ = clean_kpi_duplicate_columns(merged_data)

                after_cols = len(merged_data.columns)
                after_rows = len(merged_data)

                print(f"  合并成功: {after_rows} 行 ({before_rows} -> {after_rows})")
                print(f"  新增列数: {after_cols - before_cols}")
            else:
                print(f"  跳过：匹配率不是100% ({match_rate:.1f}%)")

        # 5. 合并计数器定义数据
        all_counter_data = []
        for file_path in excel_files:
            counter_sheet_name = find_counter_sheet(file_path)
            if counter_sheet_name:
                counter_df = pd.read_excel(file_path, sheet_name=counter_sheet_name)
                if not counter_df.empty:
                    counter_df['_source_file'] = os.path.basename(file_path)
                    all_counter_data.append(counter_df)

        merged_counters = merge_counter_data(all_counter_data)

        # 6. 合并Sheet1数据（新增功能）
        merged_sheet1 = merge_sheet1_data(excel_files)

        # 7. 创建合并后的Excel文件
        create_kpi_merged_excel_file(merged_data, merged_counters, merged_sheet1, output_file)

        print(f"✓ KPI高级合并完成: {output_file}")
        print(f"最终数据: {len(merged_data)} 行, {len(merged_data.columns)} 列")

        return output_file

    except Exception as e:
        print(f"❌ KPI高级合并失败: {str(e)}")
        print("降级到原始合并方法...")
        return merge_excel_files(excel_files, output_file)


def merge_sheet1_data(excel_files: List[str]) -> pd.DataFrame:
    """
    合并Sheet1数据（新增功能）

    Args:
        excel_files: Excel文件路径列表

    Returns:
        pd.DataFrame: 合并后的Sheet1数据，如果没有数据返回None
    """
    print("合并Sheet1数据...")

    all_sheet1_data = []

    for file_path in excel_files:
        try:
            # 检查是否有Sheet1
            xl = pd.ExcelFile(file_path)
            if 'Sheet1' in xl.sheet_names:
                sheet1_df = pd.read_excel(file_path, sheet_name='Sheet1')
                if not sheet1_df.empty:
                    sheet1_df['_source_file'] = os.path.basename(file_path)
                    all_sheet1_data.append(sheet1_df)
                    print(f"  读取Sheet1: {os.path.basename(file_path)} ({len(sheet1_df)} 行)")
        except Exception as e:
            print(f"  读取Sheet1失败: {os.path.basename(file_path)} - {str(e)}")

    if not all_sheet1_data:
        print("  没有找到Sheet1数据")
        return None

    # 合并所有Sheet1数据
    merged_sheet1 = pd.concat(all_sheet1_data, ignore_index=True, sort=False)

    # 清理内部标识字段
    merged_sheet1 = merged_sheet1.drop(columns=['_source_file'], errors='ignore')

    print(f"  Sheet1合并完成: {len(merged_sheet1)} 行")
    return merged_sheet1


def create_kpi_merged_excel_file(sheet0_data: pd.DataFrame, counter_data: pd.DataFrame,
                                sheet1_data: pd.DataFrame, output_file: str):
    """
    创建KPI合并后的Excel文件（支持Sheet1）

    Args:
        sheet0_data: 合并后的Sheet0数据
        counter_data: 合并后的计数器定义数据
        sheet1_data: 合并后的Sheet1数据
        output_file: 输出文件路径
    """
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 写入合并后的Sheet0数据
        sheet0_data.to_excel(writer, sheet_name='Sheet0', index=False)

        # 写入合并后的Sheet1数据
        if sheet1_data is not None:
            sheet1_data.to_excel(writer, sheet_name='Sheet1', index=False)

        # 写入合并后的计数器定义数据
        if counter_data is not None:
            sheet_name = 'KPI(Counter)'  # 使用标准名称
            counter_data.to_excel(writer, sheet_name=sheet_name, index=False)


# ==================== 文件处理函数 ====================

def process_file_groups(file_groups: List[List[str]]) -> List[str]:
    """
    处理文件分组：使用KPI高级合并逻辑合并可合并的分组，保留单文件分组

    Args:
        file_groups: 文件分组列表，每个分组包含可以合并的文件路径

    Returns:
        List[str]: 处理后的文件路径列表（包含合并后的文件和单独的文件）

    处理逻辑：
    1. 多文件分组：使用KPI高级合并逻辑执行合并操作，生成新的合并文件
    2. 单文件分组：检查是否只有表头，如果是则跳过，否则直接使用原文件
    3. 合并失败：降级使用分组中的第一个文件
    """
    processed_files = []

    print(f"\n处理 {len(file_groups)} 个文件分组...")

    for i, group in enumerate(file_groups):
        if len(group) > 1:
            # 多文件分组，需要合并
            print(f"分组 {i+1}: 合并 {len(group)} 个文件...")

            # 生成合并后的文件名
            merged_filename = generate_merged_filename(group)

            # 使用KPI高级合并逻辑执行合并操作
            try:
                merged_file = merge_kpi_files_advanced(group, merged_filename)
                processed_files.append(merged_file)
                print(f"  ✓ KPI高级合并成功: {merged_file}")
            except Exception as e:
                print(f"  ❌ KPI高级合并失败: {str(e)}")
                print(f"  降级到原始合并方法...")
                try:
                    merged_file = merge_excel_files(group, merged_filename)
                    processed_files.append(merged_file)
                    print(f"  ✓ 原始合并成功: {merged_file}")
                except Exception as e2:
                    print(f"  ❌ 原始合并也失败: {str(e2)}")
                    print(f"  使用第一个文件: {group[0]}")
                    processed_files.append(group[0])
        else:
            # 单文件分组，检查是否只有表头
            single_file = group[0]

            # 检查文件是否只有表头
            try:
                df = pd.read_excel(single_file, sheet_name='Sheet0')
                if df.empty:
                    print(f"分组 {i+1}: 跳过 {os.path.basename(single_file)} (仅有表头)")
                    continue
            except Exception as e:
                print(f"分组 {i+1}: 读取文件错误 {os.path.basename(single_file)}: {e}")
                continue

            processed_files.append(single_file)
            print(f"分组 {i+1}: 保留单文件 {os.path.basename(single_file)}")

    print(f"处理完成: {len(processed_files)} 个文件准备进行校验")
    return processed_files


def generate_merged_filename(excel_files: List[str]) -> str:
    """
    生成合并后的文件名

    Args:
        excel_files: 要合并的Excel文件路径列表

    Returns:
        str: 合并后的文件名

    命名规则：
    - 将所有原文件名（不含扩展名）用下划线连接
    - 添加.xlsx扩展名
    - 例如：file1.xlsx + file2.xlsx → file1_file2.xlsx
    """
    base_names = []
    for file_path in excel_files:
        # 获取不带扩展名的文件名
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        base_names.append(base_name)

    # 合并文件名，用下划线连接
    merged_name = "_".join(base_names) + ".xlsx"
    return merged_name


# ==================== Excel文件合并函数 ====================

def merge_excel_files(excel_files: List[str], output_file: str) -> str:
    """
    智能合并多个Excel文件

    支持基于内容匹配的文件合并，取消了行数限制。

    Args:
        excel_files: 要合并的Excel文件路径列表
        output_file: 输出文件路径

    Returns:
        str: 合并后的文件路径

    合并内容：
    1. Sheet0页面：具有相同非计数器字段内容的记录进行合并，计数器字段智能合并
    2. 计数器定义页面：合并所有计数器定义，去重保留最新

    特性：
    - 支持不同行数的文件合并
    - 智能处理计数器字段冲突
    - 保持数据完整性和一致性
    """
    print(f"Merging {len(excel_files)} Excel files...")

    # 存储所有文件的数据
    all_sheet0_data = []
    all_counter_data = []

    # 第一阶段：读取所有文件数据
    for i, file_path in enumerate(excel_files):
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"  Warning: File not found, skipping: {file_path}")
            continue

        # 读取Sheet0数据
        sheet0_df = pd.read_excel(file_path, sheet_name='Sheet0')
        if not sheet0_df.empty:
            # 添加源文件标识，用于追踪数据来源
            sheet0_df['_source_file'] = os.path.basename(file_path)
            all_sheet0_data.append(sheet0_df)

        # 读取计数器定义数据
        counter_sheet_name = find_counter_sheet(file_path)
        if counter_sheet_name:
            counter_df = pd.read_excel(file_path, sheet_name=counter_sheet_name)
            if not counter_df.empty:
                counter_df['_source_file'] = os.path.basename(file_path)
                all_counter_data.append(counter_df)

    # 验证是否有有效数据
    if not all_sheet0_data:
        raise ValueError("No valid Sheet0 data found in any file")

    # 第二阶段：合并Sheet0数据
    merged_sheet0 = merge_sheet0_data(all_sheet0_data)

    # 第三阶段：合并计数器定义数据
    merged_counters = merge_counter_data(all_counter_data)

    # 第四阶段：创建合并后的Excel文件
    create_merged_excel_file(merged_sheet0, merged_counters, output_file)

    print(f"✓ Merged file created: {output_file}")
    return output_file


def find_counter_sheet(file_path: str) -> str:
    """
    查找计数器定义表的名称

    Args:
        file_path: Excel文件路径

    Returns:
        str: 计数器表名称，如果未找到返回None
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Excel file not found: {file_path}")

    xl = pd.ExcelFile(file_path)
    for sheet_name in COUNTER_SHEET_NAMES:
        if sheet_name in xl.sheet_names:
            return sheet_name
    return None


def create_merged_excel_file(sheet0_data: pd.DataFrame, counter_data: pd.DataFrame, output_file: str):
    """
    创建合并后的Excel文件

    Args:
        sheet0_data: 合并后的Sheet0数据
        counter_data: 合并后的计数器定义数据
        output_file: 输出文件路径
    """
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 写入合并后的Sheet0数据
        sheet0_data.to_excel(writer, sheet_name='Sheet0', index=False)

        # 写入合并后的计数器定义数据
        if counter_data is not None:
            sheet_name = 'KPI(Counter)'  # 使用标准名称
            counter_data.to_excel(writer, sheet_name=sheet_name, index=False)


def merge_sheet0_data(all_sheet0_data: List[pd.DataFrame]) -> pd.DataFrame:
    """
    智能合并Sheet0数据

    基于非计数器字段内容进行分组合并，计数器字段按"最新非空"原则合并。

    Args:
        all_sheet0_data: 所有文件的Sheet0数据列表

    Returns:
        pd.DataFrame: 合并后的数据

    合并策略：
    1. 识别非计数器字段和计数器字段
    2. 按非计数器字段分组
    3. 计数器字段使用最后一个非空值
    4. 保持数据完整性
    """
    if not all_sheet0_data:
        return pd.DataFrame()

    # 第一步：分析所有列，区分计数器字段和非计数器字段
    all_columns = set()
    for df in all_sheet0_data:
        all_columns.update(df.columns)

    # 过滤掉内部标识字段
    non_counter_columns = [col for col in all_columns
                          if not is_counter_column(col) and col != '_source_file']
    counter_columns = [col for col in all_columns if is_counter_column(col)]

    # 第二步：合并所有数据到一个DataFrame
    combined_df = pd.concat(all_sheet0_data, ignore_index=True, sort=False)

    # 第三步：按非计数器字段分组合并
    if non_counter_columns:
        # 创建有效的分组键（确保列存在）
        groupby_columns = [col for col in non_counter_columns if col in combined_df.columns]

        if groupby_columns:
            merged_df = merge_by_non_counter_fields(combined_df, groupby_columns, counter_columns)
        else:
            merged_df = combined_df.drop(columns=['_source_file'], errors='ignore')
    else:
        merged_df = combined_df.drop(columns=['_source_file'], errors='ignore')

    return merged_df


def merge_by_non_counter_fields(combined_df: pd.DataFrame,
                               groupby_columns: List[str],
                               counter_columns: List[str]) -> pd.DataFrame:
    """
    按非计数器字段分组合并数据

    Args:
        combined_df: 合并的原始数据
        groupby_columns: 分组字段
        counter_columns: 计数器字段

    Returns:
        pd.DataFrame: 合并后的数据
    """
    merged_rows = []
    group_count = 0

    # 遍历每个分组
    for group_key, group_df in combined_df.groupby(groupby_columns):
        group_count += 1

        # 创建合并后的行
        merged_row = {}

        # 复制非计数器字段（使用第一行的值，因为分组内这些值相同）
        first_row = group_df.iloc[0]
        for col in groupby_columns:
            merged_row[col] = first_row[col]

        # 智能合并计数器字段
        for col in counter_columns:
            if col in group_df.columns:
                merged_value = merge_counter_values(group_df[col])
                merged_row[col] = merged_value

        merged_rows.append(merged_row)

    # 创建合并后的DataFrame
    merged_df = pd.DataFrame(merged_rows)

    return merged_df


def merge_counter_values(values: pd.Series) -> Any:
    """
    合并计数器字段的值

    Args:
        values: 计数器字段的值序列

    Returns:
        Any: 合并后的值

    合并策略：
    1. 优先使用非空值
    2. 如果有多个非空值，使用最后一个（最新的）
    3. 如果全为空，返回None
    """
    # 获取所有非空值
    non_null_values = values.dropna()

    if not non_null_values.empty:
        # 使用最后一个非空值（假设后面的文件包含更新的数据）
        return non_null_values.iloc[-1]
    else:
        # 如果全为空，返回None
        return None


def merge_counter_data(all_counter_data: List[pd.DataFrame]) -> pd.DataFrame:
    """
    智能合并计数器定义数据

    合并所有文件的计数器定义，去重并保留最新的定义。

    Args:
        all_counter_data: 所有文件的计数器定义数据列表

    Returns:
        pd.DataFrame: 合并后的计数器定义数据，如果没有数据返回None

    合并策略：
    1. 识别计数器ID列
    2. 按计数器ID去重，保留最后出现的记录（最新定义）
    3. 清理内部标识字段
    """
    if not all_counter_data:
        return None

    # 第一步：合并所有计数器定义数据
    combined_counters = pd.concat(all_counter_data, ignore_index=True, sort=False)

    # 第二步：识别计数器ID列
    counter_id_col = find_counter_id_column(combined_counters)

    # 第三步：根据是否找到ID列采用不同的去重策略
    if counter_id_col:
        merged_counters = deduplicate_by_counter_id(combined_counters, counter_id_col)
    else:
        merged_counters = deduplicate_simple(combined_counters)

    # 第四步：清理内部标识字段
    merged_counters = merged_counters.drop(columns=['_source_file'], errors='ignore')

    return merged_counters


def find_counter_id_column(df: pd.DataFrame) -> str:
    """
    查找计数器ID列

    Args:
        df: 计数器定义数据

    Returns:
        str: 计数器ID列名，如果未找到返回None
    """
    for col in df.columns:
        col_str = str(col).lower()
        if any(keyword in col_str for keyword in COUNTER_ID_KEYWORDS):
            return col
    return None


def deduplicate_by_counter_id(df: pd.DataFrame, counter_id_col: str) -> pd.DataFrame:
    """
    按计数器ID去重

    Args:
        df: 计数器定义数据
        counter_id_col: 计数器ID列名

    Returns:
        pd.DataFrame: 去重后的数据
    """
    # 按计数器ID去重，保留最后出现的记录（最新定义）
    merged_counters = df.drop_duplicates(subset=[counter_id_col], keep='last')
    return merged_counters


def deduplicate_simple(df: pd.DataFrame) -> pd.DataFrame:
    """
    简单去重（当无法识别计数器ID列时）

    Args:
        df: 计数器定义数据

    Returns:
        pd.DataFrame: 去重后的数据
    """
    merged_counters = df.drop_duplicates()
    return merged_counters


def load_rules() -> List[Dict[str, Any]]:
    """Load and parse the rules from the built-in rules dictionary."""
    try:
        # 使用内置规则字典
        rules_data = VALIDATION_RULES

        # 适配规则结构：从 rules.rules 数组中加载规则
        if isinstance(rules_data, dict) and 'rules' in rules_data:
            rules = rules_data['rules']
        elif isinstance(rules_data, list):
            # 兼容旧格式
            rules = rules_data
        else:
            raise ValueError("Invalid built-in rules format")

        # 将 'id' 字段映射为 'rule_id' 以保持兼容性
        for rule in rules:
            if 'id' in rule and 'rule_id' not in rule:
                rule['rule_id'] = rule['id']

        return rules
    except Exception as e:
        print(f"Error loading built-in rules: {e}")
        sys.exit(1)


def load_counter_sheet(excel_file: str) -> set:
    """
    Load counters from the counter sheet of the Excel file.
    Looks for sheets named "指标(计数器)" or "KPI(Counter)".
    Returns a set of available counter IDs.
    """
    if not os.path.exists(excel_file):
        raise FileNotFoundError(f"Excel file not found: {excel_file}")

    # Check for possible counter sheet names
    xl = pd.ExcelFile(excel_file)
    counter_sheet_name = None
    possible_sheet_names = ["指标(计数器)", "KPI(Counter)"]

    for sheet_name in possible_sheet_names:
        if sheet_name in xl.sheet_names:
            counter_sheet_name = sheet_name
            break

    if counter_sheet_name is None:
        print(f"WARNING: No counter sheet found in {excel_file}")
        return set()

    # Load the counter sheet
    counter_df = pd.read_excel(excel_file, sheet_name=counter_sheet_name)

    # Check if the sheet is empty
    if counter_df.empty:
        print(f"WARNING: '{counter_sheet_name}' sheet in {excel_file} is empty")
        return set()

    # Find the column containing counter IDs
    counter_id_col = None
    for col in counter_df.columns:
        col_str = str(col).lower()
        if any(keyword.lower() in col_str for keyword in COUNTER_ID_KEYWORDS):
            counter_id_col = col
            break

    if counter_id_col is None:
        print(f"WARNING: Could not identify counter ID column in '{counter_sheet_name}' sheet")
        return set()

    # Extract counter IDs
    counter_ids = set()
    for counter_id in counter_df[counter_id_col]:
        if pd.notna(counter_id):
            # Extract the base counter ID (remove any brackets and text after colon)
            counter_id_str = str(counter_id).strip()
            # Handle counters with brackets like "C600000001[9]"
            bracket_match = re.match(r'^([A-Za-z0-9]+)(\[\d+\])?', counter_id_str)
            if bracket_match:
                if bracket_match.group(2):  # If there's a bracket part
                    counter_ids.add(bracket_match.group(1) + bracket_match.group(2))
                else:
                    counter_ids.add(bracket_match.group(1))
            else:
                # For other formats, just add the ID
                counter_ids.add(counter_id_str)

    print(f"Loaded {len(counter_ids)} counters from '{counter_sheet_name}' sheet")
    return counter_ids


def load_excel_data(excel_file: str) -> pd.DataFrame:
    """Load data from the Excel file."""
    if not os.path.exists(excel_file):
        raise FileNotFoundError(f"Excel file not found: {excel_file}")

    # Load Sheet0 which contains the counter data
    df = pd.read_excel(excel_file, sheet_name='Sheet0')

    # 检查Sheet0是否为空或只有表头没有数据行
    if df.empty:
        # 检查是否有表头
        raw_df = pd.read_excel(excel_file, sheet_name='Sheet0', header=None)
        if not raw_df.empty and len(raw_df) == 1:
            print(f"INFO: Sheet0 in {excel_file} has headers but no data rows - skipping processing")
        else:
            print(f"INFO: Sheet0 in {excel_file} is completely empty - skipping processing")

    return df


def extract_counter_id(column_name: str) -> str:
    """Extract counter ID from column name."""
    # 处理带方括号的计数器 ID，如 "C600000001[9]:..."
    bracket_match = re.match(r'^([A-Za-z0-9]+)(\[\d+\])?:', str(column_name))
    if bracket_match:
        # 如果有方括号，返回完整的 ID 包括方括号部分
        if bracket_match.group(2):
            return bracket_match.group(1) + bracket_match.group(2)
        # 否则只返回基本 ID
        return bracket_match.group(1)

    # 处理普通计数器 ID，如 "668002:..."
    simple_match = re.match(r'^([^:]+)', str(column_name))
    if simple_match:
        return simple_match.group(1)

    return None


def get_available_counters(df: pd.DataFrame) -> Dict[str, str]:
    """Get mapping of counter IDs to their column names."""
    counter_map = {}
    for col in df.columns:
        counter_id = extract_counter_id(str(col))
        if counter_id and ':' in str(col):
            counter_map[counter_id] = col
    return counter_map


def parse_condition(condition, counter_map: Dict[str, str]) -> Tuple[str, bool]:
    """
    Parse a condition (string or dict) and replace counter IDs with column names.
    Returns the parsed condition and a flag indicating if all counters were found.
    """
    all_counters_found = True

    # 处理新格式的条件（字典格式）
    if isinstance(condition, dict):
        if 'expression' in condition:
            # 处理表达式条件
            expression = condition['expression']
            operator = condition.get('operator', '=')
            value = condition.get('value', True)

            # 解析表达式中的计数器ID
            parsed_expression = expression
            counter_ids = re.findall(r'[A-Za-z0-9]+(?:\[\d+\])?', expression)

            # 按长度降序排序，避免短ID被长ID的替换影响
            counter_ids = sorted(set(counter_ids), key=len, reverse=True)

            for counter_id in counter_ids:
                if counter_id in counter_map:
                    # 使用单词边界确保精确替换
                    pattern = r'\b' + re.escape(counter_id) + r'\b'
                    replacement = f"row['{counter_map[counter_id]}']"
                    parsed_expression = re.sub(pattern, replacement, parsed_expression)
                else:
                    # 跳过常见词汇和数字
                    if counter_id.lower() in ['and', 'or', 'not', 'true', 'false', 'none']:
                        continue
                    if counter_id.isdigit():
                        continue
                    # 计数器未找到
                    all_counters_found = False

            # 构建完整的条件表达式
            if operator == '=' and value is True:
                parsed_condition = f"({parsed_expression})"
            elif operator == '=' and value is False:
                parsed_condition = f"not ({parsed_expression})"
            else:
                parsed_condition = f"({parsed_expression}) {operator} {value}"

        elif 'counter' in condition:
            # 处理单个计数器条件
            counter_ref = condition['counter']
            operator = condition.get('operator', '=')
            value = condition.get('value', 0)

            # 尝试直接匹配计数器ID，如果不存在则尝试提取ID
            if counter_ref in counter_map:
                parsed_condition = f"row['{counter_map[counter_ref]}'] {operator} {value}"
            else:
                # 尝试从计数器名称中提取ID
                counter_id = extract_counter_id_from_name(counter_ref)
                if counter_id in counter_map:
                    parsed_condition = f"row['{counter_map[counter_id]}'] {operator} {value}"
                else:
                    all_counters_found = False
                    parsed_condition = f"False"  # 计数器不存在时返回False
        else:
            # 未知的条件格式
            parsed_condition = "False"
            all_counters_found = False
    else:
        # 处理旧格式的条件（字符串格式）
        parsed_condition = str(condition)

        # Find all counter IDs in the condition
        counter_ids = re.findall(r'[A-Za-z0-9]+(?:\[\d+\])?', parsed_condition)

        for counter_id in counter_ids:
            if counter_id in counter_map:
                # Replace counter ID with its column name
                parsed_condition = parsed_condition.replace(
                    counter_id, f"row['{counter_map[counter_id]}']"
                )
            else:
                # Skip common words that might be mistaken for counter IDs
                if counter_id.lower() in ['and', 'or', 'not', 'true', 'false', 'none']:
                    continue

                # Skip numeric values
                if counter_id.isdigit():
                    continue

                # If counter not found in data, mark as missing
                all_counters_found = False

    # Handle percentage values in the condition
    # We need to be smart about percentage handling based on the data format
    # For now, we'll convert percentages to decimals for comparison with decimal data
    parsed_condition = re.sub(r'(\d+(?:\.\d+)?)%', lambda m: str(float(m.group(1)) / 100), parsed_condition)

    return parsed_condition, all_counters_found


def evaluate_condition(condition: str, row: pd.Series) -> bool:
    """Evaluate a condition for a given row."""
    if not condition or not condition.strip():
        raise ValueError("Condition cannot be empty")

    # Create a copy of the row to avoid modifying the original
    row_copy = row.copy()

    # Handle percentage values in the data
    for col in row_copy.index:
        if isinstance(row_copy[col], str) and '%' in row_copy[col]:
            # Convert string percentages to numerical values (e.g., "95.0%" -> 95.0)
            row_copy[col] = float(row_copy[col].replace('%', ''))
        elif pd.isna(row_copy[col]):
            # Replace NaN values with 0 to avoid evaluation errors
            row_copy[col] = 0
        # Note: Numerical percentage values (e.g., 95.0) are kept as-is

    # Evaluate the condition using the modified row
    result = eval(condition, {"__builtins__": {}}, {"row": row_copy, "np": np})
    return bool(result)


def extract_counter_id_from_name(counter_name: str) -> str:
    """从计数器名称中提取计数器ID"""
    # 处理格式如 "668166 小区下行PDCP层平均流量(Mbps)"
    match = re.match(r'^([A-Za-z0-9]+(?:\[\d+\])?)', counter_name.strip())
    if match:
        return match.group(1)
    return counter_name.strip()


def filter_rules_by_counters(rules: List[Dict[str, Any]], available_counters: set) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Filter rules based on available counters.
    Returns a tuple of (valid_rules, discarded_rules).
    """
    valid_rules = []
    discarded_rules = []

    for rule in rules:
        rule_id = rule['rule_id']
        rule_counter_names = rule['counters']

        # 从计数器名称中提取计数器ID
        rule_counter_ids = set()
        for counter_name in rule_counter_names:
            counter_id = extract_counter_id_from_name(counter_name)
            rule_counter_ids.add(counter_id)

        # Check if all counters required by the rule are available
        missing_counter_ids = rule_counter_ids - available_counters

        if missing_counter_ids:
            # Some counters are missing, discard the rule
            discarded_rule = rule.copy()
            # 找出缺失的计数器名称
            missing_counter_names = []
            for counter_name in rule_counter_names:
                counter_id = extract_counter_id_from_name(counter_name)
                if counter_id in missing_counter_ids:
                    missing_counter_names.append(counter_name)
            discarded_rule['missing_counters'] = missing_counter_names
            discarded_rules.append(discarded_rule)
        else:
            # All counters are available, keep the rule
            valid_rules.append(rule)

    return valid_rules, discarded_rules


def validate_data(df: pd.DataFrame, rules: List[Dict[str, Any]]) -> pd.DataFrame:
    """
    Validate each row of data against the rules.
    Returns a DataFrame with validation results for each rule.
    """
    # 检查数据是否为空
    if df.empty:
        print("WARNING: The input data has no rows. Skipping validation.")
        return pd.DataFrame()

    # Get available counters in the data
    counter_map = get_available_counters(df)

    # 获取非计数器字段
    non_counter_columns = []
    for col in df.columns:
        if ':' not in str(col):
            non_counter_columns.append(col)

    # 创建结果DataFrame，从原始数据复制非计数器字段
    result_df = df[non_counter_columns].copy()

    # 为每个规则添加一列
    rule_columns = []
    for rule in rules:
        rule_id = rule['rule_id']
        rule_column = f"Rule{rule_id}"
        rule_columns.append(rule_column)
        result_df[rule_column] = "未验证"  # 默认值

    # 添加总校验结果列
    result_df["总校验结果"] = "未验证"

    # 直接处理所有行（不按小区分组）
    for idx, row in df.iterrows():
        # 为每个规则验证这一行
        for rule in rules:
            rule_id = rule['rule_id']
            rule_column = f"Rule{rule_id}"
            validation_result = validate_single_row_rule(row, rule, counter_map)
            result_df.loc[idx, rule_column] = validation_result

        # 计算总校验结果
        overall_result = calculate_overall_result(result_df.loc[idx], rule_columns)
        result_df.loc[idx, "总校验结果"] = overall_result

    return result_df


def calculate_overall_result(row_data, rule_columns) -> str:
    """
    计算总校验结果
    只有所有待校验规则都"通过"时才为"成功"，否则为"失败"
    如果所有规则都被跳过，则为"跳过"
    """
    rule_results = []
    for rule_col in rule_columns:
        if rule_col in row_data:
            rule_results.append(row_data[rule_col])

    # 如果没有规则结果，返回"跳过"
    if not rule_results:
        return "跳过"

    # 统计各种结果
    passed_count = rule_results.count("通过")
    failed_count = rule_results.count("失败")
    skipped_count = rule_results.count("跳过")

    # 如果所有规则都被跳过，总结果为"跳过"
    if skipped_count == len(rule_results):
        return "跳过"

    # 如果有任何规则失败，总结果为"失败"
    if failed_count > 0:
        return "失败"

    # 如果有通过的规则且没有失败的规则，总结果为"成功"
    if passed_count > 0:
        return "成功"

    # 其他情况（理论上不应该到达这里）
    return "未知"


def validate_single_row_rule(row, rule, counter_map) -> str:
    """
    验证单行数据对单个规则的结果
    返回: "通过", "失败", "跳过"
    """
    rule_id = rule['rule_id']

    # 跳过空规则
    if not rule['counters'] and not rule['necessary_conditions'] and not rule['judgment_conditions']:
        return "跳过"

    # 检查是否所有必需的计数器都可用
    missing_counters = []
    for counter_name in rule['counters']:
        counter_id = extract_counter_id_from_name(counter_name)
        if counter_id not in counter_map:
            missing_counters.append(counter_name)

    if missing_counters:
        return "跳过"

    # 评估必要条件
    necessary_conditions_met = True
    for condition in rule['necessary_conditions']:
        parsed_condition, all_counters_found = parse_condition(condition, counter_map)
        if not all_counters_found or not evaluate_condition(parsed_condition, row):
            necessary_conditions_met = False
            break

    if not necessary_conditions_met:
        return "失败"

    # 评估判决条件
    judgment_conditions_met = True
    for condition in rule['judgment_conditions']:
        parsed_condition, all_counters_found = parse_condition(condition, counter_map)
        if not all_counters_found or not evaluate_condition(parsed_condition, row):
            judgment_conditions_met = False
            break

    if judgment_conditions_met:
        return "通过"
    else:
        return "失败"


def validate_single_file(excel_file: str):
    """对单个文件进行KPI验证"""
    print(f"Loading built-in validation rules...")
    rules = load_rules()

    print(f"Loaded {len(rules)} rules")

    # Load counters from counter sheet
    print(f"Checking available counters in counter sheet...")
    available_counters = load_counter_sheet(excel_file)

    # Filter rules based on available counters
    discarded_rules = []
    if available_counters:
        print(f"Filtering rules based on {len(available_counters)} available counters")
        valid_rules, discarded_rules = filter_rules_by_counters(rules, available_counters)
        print(f"Selected {len(valid_rules)} rules for validation, discarded {len(discarded_rules)} rules")

        # If all rules are discarded, still create Summary but skip validation
        if not valid_rules:
            print("WARNING: All rules have been discarded due to missing counters")

            # Create Summary DataFrame for discarded rules only
            summary_data = []
            for rule in discarded_rules:
                summary_data.append({
                    'Rule ID': f"Rule {rule['rule_id']}",
                    'Rule Description': rule['description'],
                    'Status': '未匹配',
                    'Required Counters': ', '.join(rule['counters']),
                    'Missing Counters': ', '.join(rule['missing_counters']),
                    'Reason': f"缺少计数器: {', '.join(rule['missing_counters'])}"
                })

            summary_df = pd.DataFrame(summary_data)

            # Write Summary to Excel file
            from openpyxl import load_workbook
            from openpyxl.utils.dataframe import dataframe_to_rows

            wb = load_workbook(excel_file)

            # Check and remove existing Summary sheet
            if 'Summary' in wb.sheetnames:
                wb.remove(wb['Summary'])

            # Create new Summary sheet
            ws_summary = wb.create_sheet('Summary')

            # Write Summary data
            for r in dataframe_to_rows(summary_df, index=False, header=True):
                ws_summary.append(r)

            wb.save(excel_file)
            print(f"Summary information added to 'Summary' sheet in {excel_file}")

            print(f"Summary: Rules matched: 0, Rules discarded: {len(discarded_rules)}")

            return
    else:
        # No counters found in counter sheet, use all rules
        print("WARNING: No counters found in counter sheet. Using all rules")
        valid_rules = rules

    print(f"Loading data from {excel_file}...")
    df = load_excel_data(excel_file)
    print(f"Loaded data shape: {df.shape}")

    # 检查数据是否为空或只有表头
    if df.empty:
        print("INFO: Sheet0 has no data rows - skipping all validation processing")
        return

    print("Validating data...")
    results_df = validate_data(df, valid_rules)

    if results_df.empty:
        print("No validation results generated")
        return

    # 将结果写入原文件的新 sheet 页
    print(f"Adding validation results to {excel_file}...")

    # 使用 openpyxl 直接操作现有文件，保留原有格式
    from openpyxl import load_workbook
    from openpyxl.utils.dataframe import dataframe_to_rows

    # 加载现有的工作簿
    wb = load_workbook(excel_file)

    # 检查是否已存在"校验结果" sheet，如果存在则删除
    if '校验结果' in wb.sheetnames:
        wb.remove(wb['校验结果'])

    # 创建新的"校验结果" sheet
    ws_results = wb.create_sheet('校验结果')

    # 将校验结果数据写入 sheet
    for r in dataframe_to_rows(results_df, index=False, header=True):
        ws_results.append(r)

        # 创建Summary DataFrame
        summary_data = []

        # 添加匹配的规则
        for rule in valid_rules:
            summary_data.append({
                'Rule ID': f"Rule {rule['rule_id']}",
                'Rule Description': rule['description'],
                'Status': '已匹配',
                'Required Counters': ', '.join(rule['counters']),
                'Missing Counters': '',
                'Reason': '所有必需计数器都可用'
            })

        # 添加未匹配的规则
        for rule in discarded_rules:
            summary_data.append({
                'Rule ID': f"Rule {rule['rule_id']}",
                'Rule Description': rule['description'],
                'Status': '未匹配',
                'Required Counters': ', '.join(rule['counters']),
                'Missing Counters': ', '.join(rule['missing_counters']),
                'Reason': f"缺少计数器: {', '.join(rule['missing_counters'])}"
            })

        # 创建Summary DataFrame
        summary_df = pd.DataFrame(summary_data)

        # 检查是否已存在"Summary" sheet，如果存在则删除
        if 'Summary' in wb.sheetnames:
            wb.remove(wb['Summary'])

        # 创建新的"Summary" sheet
        ws_summary = wb.create_sheet('Summary')

        # 将Summary数据写入 sheet
        for r in dataframe_to_rows(summary_df, index=False, header=True):
            ws_summary.append(r)

        # 保存文件
        wb.save(excel_file)

        print(f"Validation results added to '校验结果' sheet in {excel_file}")
        print(f"Summary information added to 'Summary' sheet in {excel_file}")

        # 打印摘要
        total_rows = len(results_df)
        rule_columns = [col for col in results_df.columns if col.startswith('Rule')]

        print(f"Validation Summary: {total_rows} rows, {len(rule_columns)} rules validated, {len(valid_rules)} matched, {len(discarded_rules)} discarded")

        # 统计总校验结果
        if "总校验结果" in results_df.columns:
            overall_results = results_df["总校验结果"].value_counts()
            print(f"Overall results: {dict(overall_results)}")


def main():
    """Main function to run the validation."""
    if len(sys.argv) < 2:
        print("Usage: python kpi_validation.py <directory_path>")
        print("  Example: python kpi_validation.py /path/to/excel/files")
        print("  The directory should contain Excel files (.xlsx, .xls, .xlsm)")
        print("  Validation rules are built into the script")
        sys.exit(1)

    directory_path = sys.argv[1]

    # 扫描目录中的Excel文件
    excel_files = scan_excel_files(directory_path)

    if not excel_files:
        print(f"ERROR: No Excel files found in directory: {directory_path}")
        sys.exit(1)

    print(f"Directory mode: Found {len(excel_files)} Excel files")

    # 对文件进行分组（使用KPI智能分组）
    file_groups = group_files_for_kpi_merge(excel_files)

    if not file_groups:
        print("ERROR: No valid files found for processing")
        sys.exit(1)

    # 处理文件分组（合并可合并的，保留单独的）
    processed_files = process_file_groups(file_groups)

    # 对每个处理后的文件进行校验
    for i, file_path in enumerate(processed_files):
        print(f"\nProcessing file {i+1}/{len(processed_files)}: {os.path.basename(file_path)}")
        validate_single_file(file_path)

    print(f"All files processed successfully!")


if __name__ == "__main__":
    main()
