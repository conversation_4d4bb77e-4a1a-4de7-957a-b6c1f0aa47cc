#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
KPI数据校验工具 v3.0 - 集成智能合并功能

功能特性：
1. 集成KPI数据合并工具的智能合并功能
2. 支持小区级别文件的智能识别和筛选
3. 基于小区字段进行列追加合并（364行左右的合并结果）
4. 支持Sheet0、Sheet1和计数器定义页面的完整合并
5. 灵活的规则引擎：支持复杂的条件表达式和计数器比较
6. 完整的数据保护：保留所有非计数器字段和计数器字段
7. 详细的校验结果报告：包含规则匹配状态和缺失计数器信息

校验原则：
1) 必要条件必须满足才能进行判决条件评估
2) 判决条件按顺序评估，所有条件都必须满足才能通过
3) 支持表达式计算和复杂的逻辑判断

合并原则：
1) 智能识别小区级别数据，排除网元级别数据
2) 基于时间段和小区ID进行列追加合并
3) 排除包含特定关键字的文件（波束信息ID、可替换单元ID）
4) 支持中英文字段映射和智能字段检测

使用方法：
python kpi_validation_integrated.py <directory_path>

示例：
python kpi_validation_integrated.py /path/to/excel/files
python kpi_validation_integrated.py "D:\Data\Excel Files"

作者：Augment Agent
版本：3.0
更新：集成KPI数据合并工具功能，支持Sheet1合并
"""

import pandas as pd
import re
import numpy as np
import sys
import os
import glob
import warnings
from typing import List, Dict, Any, Tuple

warnings.filterwarnings('ignore')

# ==================== 常量定义 ====================

# 支持的计数器表名称
COUNTER_SHEET_NAMES = ["指标(计数器)", "KPI(Counter)"]

# 计数器ID列识别关键词
COUNTER_ID_KEYWORDS = ["指标或计数器", "kpi or counter"]

# 校验结果状态
VALIDATION_STATUS = {
    'PASS': '通过',
    'FAIL': '失败',
    'SKIP': '跳过',
    'NOT_VALIDATED': '未验证'
}

# 总校验结果状态
OVERALL_STATUS = {
    'SUCCESS': '成功',
    'FAILURE': '失败',
    'SKIP': '跳过',
    'UNKNOWN': '未知'
}

# KPI合并工具配置
EXCLUDE_KEYWORDS = ['波束信息ID', '可替换单元ID']
CELL_FIELDS = ['CU小区配置ID', 'nrPhysicalCellDUId', 'NRCarrierId']
BASE_FIELDS = ['开始时间', '结束时间', '粒度', 'SubnetWork ID', 'SubnetWork Name', 'ManagedElement ID', '管理网元']

# 字段映射（中英文对照）
FIELD_MAPPINGS = {
    # 排除关键字映射
    'EXCLUDE_KEYWORDS': {
        '波束信息ID': ['波束信息ID'],
        '可替换单元ID': ['可替换单元ID', 'ReplaceableUnitId', 'Replaceable Unit ID']
    },

    # 小区字段映射
    'CELL_FIELDS': {
        'CU小区配置ID': ['CU小区配置ID'],
        'nrPhysicalCellDUId': ['nrPhysicalCellDUId'],
        'NRCarrierId': ['NRCarrierId']
    },

    # 基础字段映射
    'BASE_FIELDS': {
        '开始时间': ['开始时间', 'Start Time', 'StartTime'],
        '结束时间': ['结束时间', 'End Time', 'EndTime'],
        '粒度': ['粒度', 'Granularity Period', 'GranularityPeriod'],
        'SubnetWork ID': ['SubnetWork ID', 'SubnetworkId', '子网ID', 'Subnetwork ID'],
        'SubnetWork Name': ['SubnetWork Name', 'SubnetworkName', '子网名称', 'Subnetwork Name'],
        'ManagedElement ID': ['ManagedElement ID', 'ManagedElementId', '网元ID', 'Managed Element ID'],
        '管理网元': ['管理网元']
    }
}

# 内置校验规则
VALIDATION_RULES = {
    "rules": [
        {
            "id": 1,
            "counters": [
                "668166 小区下行PDCP层平均流量(Mbps)",
                "1711020040323 下行MAC层流量-高频_1711073545754",
                "668002 小区下行 PDCP SDU弃包率(%)"
            ],
            "necessary_conditions": [
                {"counter": "668166", "operator": ">", "value": 0},
                {"counter": "1711020040323", "operator": ">", "value": 0}
            ],
            "judgment_conditions": [
                {"counter": "668002", "operator": "=", "value": 0},
                {"expression": "(1711020040323-668166)/668166", "operator": ">=", "value": 0},
                {"expression": "(1711020040323-668166)/668166", "operator": "<", "value": 25}
            ],
            "description": "PDCP弃包率为0%时，下行padding率大于0%且小于25%"
        },
        {
            "id": 2,
            "counters": [
                "668166 小区下行PDCP层平均流量(Mbps)",
                "668002 小区下行 PDCP SDU弃包率(%)",
                "668001 小区下行CUUP平均时延(ms)",
                "669503 小区下行RLC SDU平均时延(ms)",
                "669504 小区下行Ip平均时延(ms)"
            ],
            "necessary_conditions": [
                {"counter": "668166", "operator": ">", "value": 0},
                {"counter": "668002", "operator": "=", "value": 0}
            ],
            "judgment_conditions": [
                {"counter": "668001", "operator": "<", "value": 5},
                {"expression": "669503 < 669504", "operator": "=", "value": True},
                {"counter": "669503", "operator": "<", "value": 20}
            ],
            "description": "下行无丢包时，CUUP在5ms以内，IP时延小于RLC SDU，RLC SDU通常在20ms以内"
        },
        {
            "id": 3,
            "counters": [
                "668167 小区上行PDCP层平均流量(Mbps)",
                "1711020040324 上行MAC层流量-高频_1711073545945",
                "668000 小区上行PDCP SDU丢包率(%)"
            ],
            "necessary_conditions": [
                {"counter": "668167", "operator": ">", "value": 0},
                {"counter": "1711020040324", "operator": ">", "value": 0}
            ],
            "judgment_conditions": [
                {"counter": "668000", "operator": "=", "value": 0},
                {"expression": "(1711020040324-668167)/668167", "operator": ">=", "value": 0},
                {"expression": "(1711020040324-668167)/668167", "operator": "<", "value": 200}
            ],
            "description": "PDCP弃包率为0%时，下行padding率大于0%且小于200%"
        },
        {
            "id": 4,
            "counters": [
                "668167 小区上行PDCP层平均流量(Mbps)",
                "668000 小区上行PDCP SDU丢包率(%)",
                "668018 小区上行CUUP平均时延(ms)",
                "669505 小区上行RLC SDU平均时延(ms)"
            ],
            "necessary_conditions": [
                {"counter": "668167", "operator": ">", "value": 0},
                {"counter": "668000", "operator": "=", "value": 0}
            ],
            "judgment_conditions": [
                {"counter": "668018", "operator": "<", "value": 5},
                {"counter": "669505", "operator": "<", "value": 20}
            ],
            "description": "上行无丢包时，CUUP在5ms以内，RLC SDU通常在20ms以内"
        },
        {
            "id": 5,
            "counters": [
                "668167 小区上行PDCP层平均流量(Mbps)",
                "670138 小区上行BLER(%)",
                "670033 小区上行HARQ失败比例(%)"
            ],
            "necessary_conditions": [
                {"counter": "668167", "operator": ">", "value": 0}
            ],
            "judgment_conditions": [
                {"counter": "670138", "operator": "<=", "value": 10},
                {"counter": "670033", "operator": "<=", "value": 0.5}
            ],
            "description": "目标bler10%，harq fail比例控制在千分之5以内"
        },
        {
            "id": 6,
            "counters": [
                "668166 小区下行PDCP层平均流量(Mbps)",
                "670139 小区下行BLER(%)",
                "670034 小区下行HARQ失败比例(%)"
            ],
            "necessary_conditions": [
                {"counter": "668166", "operator": ">", "value": 0}
            ],
            "judgment_conditions": [
                {"counter": "670139", "operator": "<=", "value": 10},
                {"counter": "670034", "operator": "<=", "value": 0.5}
            ],
            "description": "目标bler10%，harq fail比例控制在千分之5以内"
        },
        {
            "id": 7,
            "counters": [
                "668167 小区上行PDCP层平均流量(Mbps)",
                "C616650043 小区上行干扰电平(dBm)",
                "C616650045 小区上行干扰电平(dBm)"
            ],
            "necessary_conditions": [
                {"counter": "668167", "operator": ">", "value": 0}
            ],
            "judgment_conditions": [
                {"counter": "C616650043", "operator": "<", "value": -100},
                {"counter": "C616650045", "operator": "<", "value": -100}
            ],
            "description": "小区上行干扰电平需低于-100dBm"
        },
        {
            "id": 8,
            "counters": [
                "C616310004 小区随机接入次数",
                "C616310006 小区随机接入次数",
                "667184 小区随机接入成功率(%)",
                "667000 小区RRC连接建立成功率(%)",
                "667015 小区RRC连接重建成功率(%)"
            ],
            "necessary_conditions": [
                {"expression": "C616310004 > 0 or C616310006 > 0", "operator": "=", "value": True}
            ],
            "judgment_conditions": [
                {"counter": "667184", "operator": ">", "value": 99.9},
                {"counter": "667000", "operator": ">", "value": 99.9},
                {"counter": "667015", "operator": ">", "value": 99.9}
            ],
            "description": "有msg1统计的环境，接通率等成功率指标必须大于99.9%"
        }
    ]
}

# ==================== KPI合并工具函数 ====================

def scan_excel_files(directory_path: str) -> List[str]:
    """扫描指定目录中的所有Excel文件"""
    if not os.path.exists(directory_path):
        raise FileNotFoundError(f"Directory not found: {directory_path}")

    if not os.path.isdir(directory_path):
        raise NotADirectoryError(f"Path is not a directory: {directory_path}")

    # 支持的Excel文件扩展名
    excel_extensions = ['*.xlsx', '*.xls', '*.xlsm']
    excel_files = []

    for extension in excel_extensions:
        pattern = os.path.join(directory_path, extension)
        files = glob.glob(pattern)
        excel_files.extend(files)

    # 去重并排序
    excel_files = sorted(list(set(excel_files)))
    print(f"找到 {len(excel_files)} 个Excel文件")
    return excel_files


def is_valid_kpi_file(file_path):
    """检查文件是否为有效的KPI文件（更宽松的检查）"""
    try:
        df = pd.read_excel(file_path, sheet_name=0)

        # 检查是否有数据
        has_data = len(df) > 0
        if not has_data:
            return False, "无数据行"

        # 检查排除关键字
        columns_str = ' '.join(df.columns.astype(str))
        has_exclude = any(keyword in columns_str for keyword in EXCLUDE_KEYWORDS)
        if has_exclude:
            return False, "包含排除关键字"

        # 检查是否有任何可能的小区级别字段（更宽松的检查）
        all_possible_cell_fields = []
        for field_variants in FIELD_MAPPINGS['CELL_FIELDS'].values():
            all_possible_cell_fields.extend(field_variants)

        has_cell_fields = any(field in df.columns for field in all_possible_cell_fields)

        if not has_cell_fields:
            return False, "网元级别(无小区字段)"
        else:
            return True, "有效文件"

    except Exception as e:
        return False, f"读取错误: {str(e)}"


def detect_kpi_fields(excel_files):
    """智能检测所有文件的字段，更新全局字段变量"""
    global EXCLUDE_KEYWORDS, CELL_FIELDS, BASE_FIELDS

    print("\n智能检测KPI字段...")

    # 收集所有文件的列名
    all_columns = set()
    for file in excel_files:
        try:
            df = pd.read_excel(file, sheet_name=0)
            for col in df.columns:
                all_columns.add(str(col).strip())
        except Exception as e:
            print(f"  读取文件 {file} 失败: {str(e)}")

    #print(f"  收集到的所有列名: {sorted(list(all_columns))}")

    # 检测并更新每种字段类型
    for field_type, mapping in FIELD_MAPPINGS.items():
        detected_fields = []

        print(f"\n  检测{field_type}字段...")
        for field_key, field_variants in mapping.items():
            found = False
            found_variant = None

            print(f"    检测字段 {field_key} 的变体: {field_variants}")
            for variant in field_variants:
                variant_str = str(variant).strip()
                if variant_str in all_columns:
                    found_variant = variant_str
                    found = True
                    print(f"    ✓ 找到变体: {variant_str}")
                    break
                else:
                    print(f"    ✗ 未找到变体: {variant_str}")

            if found:
                detected_fields.append(found_variant)
                print(f"  检测到{field_type}字段: {found_variant} (对应 {field_key})")
            else:
                if field_type == 'EXCLUDE_KEYWORDS':
                    # 排除关键字如果没找到就保留原始关键字
                    detected_fields.append(field_key)
                    print(f"  未检测到排除关键字: {field_key}，保留原始关键字")
                elif field_type == 'CELL_FIELDS':
                    # 小区字段如果没找到就跳过，不抛出错误
                    print(f"  警告: 未找到小区字段 {field_key} 的任何变体")
                else:
                    # 基础字段如果没找到就保留原始字段名
                    detected_fields.append(field_key)
                    print(f"  警告: 未找到{field_type}字段 {field_key}，保留原始字段名")

        # 更新全局变量
        if field_type == 'EXCLUDE_KEYWORDS':
            EXCLUDE_KEYWORDS = detected_fields
            print(f"  更新排除关键字: {EXCLUDE_KEYWORDS}")
        elif field_type == 'CELL_FIELDS':
            if detected_fields:
                CELL_FIELDS = detected_fields
                print(f"  更新小区字段: {CELL_FIELDS}")
            else:
                # 如果没有检测到任何小区字段，使用原始配置
                print(f"  警告: 未检测到任何小区字段，保留原始配置: {CELL_FIELDS}")
        elif field_type == 'BASE_FIELDS':
            if detected_fields:
                BASE_FIELDS = detected_fields
                print(f"  更新基础字段: {BASE_FIELDS}")

    return {'EXCLUDE_KEYWORDS': EXCLUDE_KEYWORDS, 'CELL_FIELDS': CELL_FIELDS, 'BASE_FIELDS': BASE_FIELDS}


def filter_valid_kpi_files(excel_files):
    """筛选有效的KPI文件（只保留小区级别文件）"""
    valid_files = []

    print("\n检查KPI文件有效性...")
    for file in excel_files:
        is_valid, reason = is_valid_kpi_file(file)

        if is_valid:
            df = pd.read_excel(file, sheet_name=0)

            # 检查文件中实际包含的小区字段
            actual_cell_fields = []
            all_possible_cell_fields = []
            for field_variants in FIELD_MAPPINGS['CELL_FIELDS'].values():
                all_possible_cell_fields.extend(field_variants)

            for field in all_possible_cell_fields:
                if field in df.columns:
                    actual_cell_fields.append(field)

            if actual_cell_fields:
                valid_files.append({
                    'file': file,
                    'df': df,
                    'cell_fields': actual_cell_fields,
                    'rows': len(df)
                })
                print(f"✓ {os.path.basename(file)[:50]}... ({len(df)} 行, 字段: {actual_cell_fields})")
            else:
                print(f"✗ {os.path.basename(file)[:50]}... (无有效小区字段)")
        else:
            print(f"✗ {os.path.basename(file)[:50]}... ({reason})")

    print(f"\n小区级别文件: {len(valid_files)} 个")
    print(f"排除的文件: {len(excel_files) - len(valid_files)} 个")

    return valid_files


def create_kpi_merge_key(df, fields):
    """创建KPI合并键"""
    available_fields = [field for field in fields if field in df.columns]
    if available_fields:
        return df[available_fields].astype(str).apply(lambda x: '|'.join(x.values), axis=1)
    return None


def calculate_kpi_match_rate(left_keys, right_keys):
    """计算KPI匹配率"""
    if len(left_keys) == 0:
        return 0
    return len(set(left_keys) & set(right_keys)) / len(set(left_keys)) * 100


def get_kpi_cell_field_type(df):
    """获取数据框的小区字段类型（检查所有可能的小区字段）"""
    # 首先检查当前配置的小区字段
    for field in CELL_FIELDS:
        if field in df.columns:
            return field

    # 如果没找到，检查所有可能的小区字段
    all_possible_cell_fields = []
    for field_variants in FIELD_MAPPINGS['CELL_FIELDS'].values():
        all_possible_cell_fields.extend(field_variants)

    for field in all_possible_cell_fields:
        if field in df.columns:
            return field

    return None


def clean_kpi_duplicate_columns(merged_data, suffix_pattern='_追加'):
    """清理KPI合并中的重复列"""
    columns_to_drop = [col for col in merged_data.columns
                      if suffix_pattern in col and not col.startswith('数据源')]

    if columns_to_drop:
        print(f"  删除的列: {columns_to_drop}")
        merged_data = merged_data.drop(columns=columns_to_drop)
        print(f"  清理了 {len(columns_to_drop)} 个带有'{suffix_pattern}'后缀的列")

    return merged_data, len(columns_to_drop)


def prepare_kpi_merge_keys(merged_data, df, base_field_type, current_field_type):
    """准备KPI合并键（来自KPI合并工具）"""
    left_on = BASE_FIELDS.copy()
    right_on = BASE_FIELDS.copy()

    # 如果字段类型相同，直接使用相同字段合并
    if current_field_type == base_field_type:
        left_on.append(current_field_type)
        right_on.append(current_field_type)
    else:
        # 不同字段类型，使用各自的字段
        left_on.append(base_field_type)
        right_on.append(current_field_type)
    return left_on, right_on


def merge_single_kpi_file_data(merged_data, df, left_on, right_on, file_index):
    """合并单个KPI文件数据（来自KPI合并工具）"""
    before_cols = len(merged_data.columns)
    before_rows = len(merged_data)

    # 检查匹配率
    left_keys = create_kpi_merge_key(merged_data[left_on].drop_duplicates(), left_on)
    right_keys = create_kpi_merge_key(df[right_on].drop_duplicates(), right_on)

    match_rate = calculate_kpi_match_rate(left_keys, right_keys)
    print(f"  合并键匹配率: {match_rate:.1f}%")

    if match_rate == 100:  # 要求100%匹配率
        # 使用内连接合并
        merged_data = pd.merge(
            merged_data,
            df,
            left_on=left_on,
            right_on=right_on,
            how='inner',
            suffixes=('', f'_追加{file_index}')
        )

        # 清理重复的基础字段和小区字段
        merged_data, _ = clean_kpi_duplicate_columns(merged_data)

        after_cols = len(merged_data.columns)
        after_rows = len(merged_data)

        print(f"  合并成功: {after_rows} 行 ({before_rows} -> {after_rows})")
        print(f"  新增列数: {after_cols - before_cols}")

        return merged_data, True
    else:
        print(f"  跳过：匹配率不是100% ({match_rate:.1f}%)")
        return merged_data, False


def merge_all_kpi_files(valid_kpi_files, base_file_info):
    """合并所有小区级别文件，使用第一个文件作为基础（来自KPI合并工具）"""
    # 初始化合并数据为第一个文件
    merged_data = base_file_info['df'].copy()
    merged_data['数据源'] = base_file_info['file']

    print(f"\n开始合并小区级别文件...")

    # 确定基础文件的字段类型
    base_field_type = get_kpi_cell_field_type(merged_data)
    if not base_field_type:
        raise ValueError("基础文件没有找到任何小区级别字段")

    print(f"基础文件字段类型: {base_field_type}")
    processed_field_types = [base_field_type]

    # 计算预期的合并键数量：基础字段数量 + 1个小区字段
    expected_key_count = len(BASE_FIELDS) + 1

    # 合并其他文件
    for i, file_info in enumerate(valid_kpi_files):
        if file_info['file'] == base_file_info['file']:
            continue  # 跳过基础文件

        df = file_info['df'].copy()
        df['数据源'] = file_info['file']

        # 确定当前文件的字段类型
        current_field_type = get_kpi_cell_field_type(df)
        if not current_field_type:
            print(f"跳过文件: {os.path.basename(file_info['file'])[:40]}... (无小区字段)")
            continue

        print(f"\n合并文件 {i+1}: {os.path.basename(file_info['file'])[:40]}...")
        print(f"当前文件字段类型: {current_field_type}")

        # 准备合并键
        left_on, right_on = prepare_kpi_merge_keys(merged_data, df, base_field_type, current_field_type)

        # 检查合并键是否足够
        if len(left_on) < expected_key_count or len(right_on) < expected_key_count:
            print(f"跳过文件: 合并键不足 ({len(left_on)}, {len(right_on)}), 预期至少 {expected_key_count} 个")
            continue

        print(f"  左合并键: {left_on}")
        print(f"  右合并键: {right_on}")

        try:
            # 执行合并
            merged_data, success = merge_single_kpi_file_data(merged_data, df, left_on, right_on, i)

            # 如果是新的字段类型，添加到已处理列表
            if success and current_field_type not in processed_field_types:
                processed_field_types.append(current_field_type)

        except Exception as e:
            print(f"  合并失败: {str(e)}")

    print(f"\n所有文件合并完成")
    print(f"最终数据: {len(merged_data)} 行, {len(merged_data.columns)} 列")
    print(f"处理的字段类型: {processed_field_types}")

    return merged_data


def merge_kpi_files_advanced(excel_files: List[str], output_file: str, valid_kpi_files=None) -> str:
    """使用KPI合并工具的原始合并逻辑（不分组，直接合并）"""
    print(f"使用KPI原始合并逻辑合并 {len(excel_files)} 个文件...")

    try:
        # 如果没有提供有效文件列表，则进行检测
        if valid_kpi_files is None:
            # 1. 智能检测字段
            detect_kpi_fields(excel_files)

            # 2. 筛选有效文件
            valid_kpi_files = filter_valid_kpi_files(excel_files)

            if not valid_kpi_files:
                raise ValueError("没有找到符合条件的小区级别文件")
        else:
            print("使用已检测的有效文件列表，跳过重复检测")

        # 3. 使用第一个文件作为基础文件
        base_file_info = valid_kpi_files[0]
        print(f"使用基础文件: {os.path.basename(base_file_info['file'])}")
        print(f"基础文件字段: {base_file_info['cell_fields']}")

        # 4. 使用KPI合并工具的原始合并逻辑
        merged_data = merge_all_kpi_files(valid_kpi_files, base_file_info)

        # 5. 合并计数器定义数据
        print(f"\n开始合并计数器定义页面...")
        all_counter_data = []

        for file_path in excel_files:
            counter_sheet_name = find_counter_sheet(file_path)
            if counter_sheet_name:
                try:
                    counter_df = pd.read_excel(file_path, sheet_name=counter_sheet_name)
                    if not counter_df.empty:
                        counter_df['_source_file'] = os.path.basename(file_path)
                        all_counter_data.append(counter_df)
                        print(f"  ✓ 读取计数器页面: {os.path.basename(file_path)} -> {counter_sheet_name} ({len(counter_df)} 行)")
                    else:
                        print(f"  ⚠️ 计数器页面为空: {os.path.basename(file_path)} -> {counter_sheet_name}")
                except Exception as e:
                    print(f"  ❌ 读取计数器页面失败: {os.path.basename(file_path)} -> {str(e)}")
            else:
                print(f"  ✗ 未找到计数器页面: {os.path.basename(file_path)}")

        merged_counters = merge_counter_data(all_counter_data)

        # 6. 创建合并后的Excel文件
        create_kpi_merged_excel_file(merged_data, merged_counters, output_file)

        print(f"✓ KPI原始合并完成: {output_file}")
        print(f"最终数据: {len(merged_data)} 行, {len(merged_data.columns)} 列")

        # 8. 打印摘要信息（来自KPI合并工具）
        print_kpi_final_summary(merged_data)
        validate_kpi_merge_result(merged_data)

        return output_file

    except Exception as e:
        print(f"❌ KPI原始合并失败: {str(e)}")
        raise e  # 直接抛出异常，不进行降级合并


def print_kpi_final_summary(merged_data):
    """打印KPI最终摘要信息（来自KPI合并工具）"""
    print("\n" + "="*50)
    print("🎉 KPI数据合并完成！")
    print(f"最终行数: {len(merged_data)}")
    print(f"最终列数: {len(merged_data.columns)}")

    # 数据源分布
    print(f"\n📊 数据源分布:")
    if '数据源' in merged_data.columns:
        source_counts = merged_data['数据源'].value_counts()
        for source, count in source_counts.items():
            print(f"  {os.path.basename(source)[:40]}... : {count} 行")

    # 合并特点
    print(f"\n✅ 合并特点:")
    print(f"  ✓ 智能识别小区级别数据")
    print(f"  ✓ 自动适应不同文件的字段差异")
    print(f"  ✓ 基于时间和小区ID进行列追加合并")
    print(f"  ✓ 排除了网元级别数据，避免业务逻辑错误")


def validate_kpi_merge_result(merged_data):
    """验证KPI合并结果（来自KPI合并工具）"""
    print(f"\n🔍 合并结果验证:")

    unique_cells = None
    unique_times = None

    # 检查是否有小区ID字段
    cell_field = None
    for field in CELL_FIELDS:
        if field in merged_data.columns:
            cell_field = field
            break

    # 检查是否有时间字段
    time_field = None
    for field in ['开始时间', 'Start Time', 'StartTime']:
        if field in merged_data.columns:
            time_field = field
            break

    if cell_field:
        unique_cells = merged_data[cell_field].nunique()
        print(f"  物理小区数量: {unique_cells}")

    if time_field:
        unique_times = merged_data[time_field].nunique()
        print(f"  时间段数量: {unique_times}")

        if unique_cells and unique_times:
            expected_rows = unique_cells * unique_times
            print(f"  理论行数: {unique_cells} × {unique_times} = {expected_rows}")

            if len(merged_data) == expected_rows:
                print(f"  ✅ 行数验证通过")
            else:
                print(f"  ⚠️  行数不匹配，可能有重复或缺失数据")


def find_counter_sheet(file_path: str) -> str:
    """查找计数器定义表的名称"""
    if not os.path.exists(file_path):
        return None

    try:
        xl = pd.ExcelFile(file_path)
        print(f"  检查文件: {os.path.basename(file_path)}")
        print(f"    可用页面: {xl.sheet_names}")

        for sheet_name in COUNTER_SHEET_NAMES:
            if sheet_name in xl.sheet_names:
                print(f"    ✓ 找到计数器页面: {sheet_name}")
                return sheet_name

        print(f"    ✗ 未找到计数器页面 (查找: {COUNTER_SHEET_NAMES})")
        return None
    except Exception as e:
        print(f"    ❌ 读取文件失败: {str(e)}")
        return None


def merge_counter_data(all_counter_data: List[pd.DataFrame]) -> pd.DataFrame:
    """智能合并计数器定义数据"""
    print(f"\n合并计数器定义数据...")

    if not all_counter_data:
        print("  没有找到计数器定义数据")
        return None

    print(f"  找到 {len(all_counter_data)} 个计数器定义文件")

    # 显示每个文件的信息
    for i, counter_df in enumerate(all_counter_data, 1):
        source_file = counter_df.get('_source_file', ['未知'])[0] if '_source_file' in counter_df.columns else '未知'
        print(f"    文件 {i}: {source_file} ({len(counter_df)} 行, {len(counter_df.columns)} 列)")

    # 合并所有计数器定义数据
    combined_counters = pd.concat(all_counter_data, ignore_index=True, sort=False)
    print(f"  合并前总行数: {len(combined_counters)}")

    # 去重处理
    before_dedup = len(combined_counters)
    merged_counters = combined_counters.drop_duplicates()
    after_dedup = len(merged_counters)

    if before_dedup != after_dedup:
        print(f"  去重: {before_dedup} -> {after_dedup} 行 (删除 {before_dedup - after_dedup} 个重复项)")
    else:
        print(f"  无重复项，保持 {after_dedup} 行")

    # 清理内部标识字段
    merged_counters = merged_counters.drop(columns=['_source_file'], errors='ignore')

    print(f"  ✓ 计数器定义合并完成: {len(merged_counters)} 行, {len(merged_counters.columns)} 列")
    return merged_counters


# Sheet1合并功能已删除


def create_kpi_merged_excel_file(sheet0_data: pd.DataFrame, counter_data: pd.DataFrame, output_file: str):
    """创建KPI合并后的Excel文件"""
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 写入合并后的Sheet0数据
        sheet0_data.to_excel(writer, sheet_name='Sheet0', index=False)

        # 写入合并后的计数器定义数据
        if counter_data is not None:
            sheet_name = 'KPI(Counter)'
            counter_data.to_excel(writer, sheet_name=sheet_name, index=False)


# 降级合并函数已删除


def process_kpi_files_directly(excel_files: List[str]) -> List[str]:
    """直接处理KPI文件，不分组，使用KPI合并工具的原始逻辑"""
    print(f"\n直接处理 {len(excel_files)} 个KPI文件...")

    try:
        # 1. 智能检测字段（中英文适配）
        detect_kpi_fields(excel_files)

        # 2. 筛选有效的小区级别文件
        valid_kpi_files = filter_valid_kpi_files(excel_files)
        
        # 找出未参与合并的文件（即不是有效小区级别文件的文件）
        valid_file_paths = [file_info['file'] for file_info in valid_kpi_files]
        non_merged_files = [file for file in excel_files if file not in valid_file_paths]
        
        # 初始化结果列表
        result_files = []

        # 处理有效的小区级别文件
        if valid_kpi_files:
            if len(valid_kpi_files) == 1:
                # 3. 如果只有一个文件，直接添加到结果
                print(f"只有一个有效文件，直接使用: {os.path.basename(valid_kpi_files[0]['file'])}")
                result_files.append(valid_kpi_files[0]['file'])
            else:
                # 4. 多个文件，进行合并
                print(f"找到 {len(valid_kpi_files)} 个有效的小区级别文件，开始合并...")

                # 生成合并后的文件名
                file_paths = [file_info['file'] for file_info in valid_kpi_files]
                merged_filename = generate_merged_filename(file_paths)

                # 使用KPI高级合并逻辑，传入已经检测过的有效文件
                merged_file = merge_kpi_files_advanced(file_paths, merged_filename, valid_kpi_files)
                result_files.append(merged_file)
        
        # 添加未参与合并的文件
        if non_merged_files:
            print(f"找到 {len(non_merged_files)} 个未参与合并的文件")
            for file in non_merged_files:
                print(f"  添加未合并文件: {os.path.basename(file)}")
            result_files.extend(non_merged_files)
            
        return result_files

    except Exception as e:
        print(f"❌ KPI文件处理失败: {str(e)}")
        raise e  # 直接抛出异常，不进行降级处理


def generate_merged_filename(excel_files: List[str]) -> str:
    """生成合并后的文件名"""
    # 直接使用简单的文件名
    merged_name = "KPI合并文件.xlsx"
    return merged_name


# 移除了process_file_groups函数，因为现在直接处理文件，不再分组


# ==================== 校验相关函数 ====================

def load_rules() -> List[Dict[str, Any]]:
    """加载内置校验规则"""
    try:
        rules_data = VALIDATION_RULES

        if isinstance(rules_data, dict) and 'rules' in rules_data:
            rules = rules_data['rules']
        elif isinstance(rules_data, list):
            rules = rules_data
        else:
            raise ValueError("Invalid built-in rules format")

        # 将 'id' 字段映射为 'rule_id' 以保持兼容性
        for rule in rules:
            if 'id' in rule and 'rule_id' not in rule:
                rule['rule_id'] = rule['id']

        return rules
    except Exception as e:
        print(f"Error loading built-in rules: {e}")
        sys.exit(1)


def is_counter_column(column_name: str) -> bool:
    """判断列是否为计数器字段"""
    return ':' in str(column_name)


def extract_counter_id(column_name: str) -> str:
    """从列名中提取计数器ID"""
    # 处理带方括号的计数器 ID，如 "C600000001[9]:..."
    bracket_match = re.match(r'^([A-Za-z0-9]+)(\[\d+\])?:', str(column_name))
    if bracket_match:
        if bracket_match.group(2):
            return bracket_match.group(1) + bracket_match.group(2)
        return bracket_match.group(1)

    # 处理普通计数器 ID，如 "668002:..."
    simple_match = re.match(r'^([^:]+)', str(column_name))
    if simple_match:
        return simple_match.group(1)

    return None


def get_available_counters(df: pd.DataFrame) -> Dict[str, str]:
    """获取可用计数器的映射"""
    counter_map = {}
    for col in df.columns:
        counter_id = extract_counter_id(str(col))
        if counter_id and ':' in str(col):
            counter_map[counter_id] = col
    return counter_map


def extract_counter_id_from_name(counter_name: str) -> str:
    """从计数器名称中提取计数器ID"""
    match = re.match(r'^([A-Za-z0-9]+(?:\[\d+\])?)', counter_name.strip())
    if match:
        return match.group(1)
    return counter_name.strip()


def parse_condition(condition, counter_map: Dict[str, str]) -> Tuple[str, bool]:
    """
    解析条件（字符串或字典）并替换计数器ID为列名。
    返回解析后的条件和一个标志，指示是否找到所有计数器。
    """
    all_counters_found = True

    # 处理新格式的条件（字典格式）
    if isinstance(condition, dict):
        if 'expression' in condition:
            # 处理表达式条件
            expression = condition['expression']
            operator = condition.get('operator', '==')  # 默认使用==而不是=
            value = condition.get('value', True)

            # 解析表达式中的计数器ID
            parsed_expression = expression
            counter_ids = re.findall(r'[A-Za-z0-9]+(?:\[\d+\])?', expression)

            # 按长度降序排序，避免短ID被长ID的替换影响
            counter_ids = sorted(set(counter_ids), key=len, reverse=True)

            for counter_id in counter_ids:
                if counter_id in counter_map:
                    # 使用单词边界确保精确替换
                    pattern = r'\b' + re.escape(counter_id) + r'\b'
                    replacement = f"row['{counter_map[counter_id]}']"
                    parsed_expression = re.sub(pattern, replacement, parsed_expression)
                else:
                    # 跳过常见词汇和数字
                    if counter_id.lower() in ['and', 'or', 'not', 'true', 'false', 'none']:
                        continue
                    if counter_id.isdigit():
                        continue
                    # 计数器未找到
                    all_counters_found = False

            # 构建完整的条件表达式
            if operator == '=' and value is True:
                # 修正：使用==而不是=
                parsed_condition = f"({parsed_expression})"
            elif operator == '=' and value is False:
                parsed_condition = f"not ({parsed_expression})"
            else:
                # 确保operator不是赋值符号
                if operator == '=':
                    operator = '=='
                parsed_condition = f"({parsed_expression}) {operator} {value}"

        elif 'counter' in condition:
            # 处理单个计数器条件
            counter_ref = condition['counter']
            operator = condition.get('operator', '=')
            value = condition.get('value', 0)
            
            # 删除对rule的引用，百分比处理移到validate_single_row_rule函数中
            
            # 确保operator不是赋值符号
            if operator == '=':
                operator = '=='

            # 尝试直接匹配计数器ID，如果不存在则尝试提取ID
            if counter_ref in counter_map:
                parsed_condition = f"row['{counter_map[counter_ref]}'] {operator} {value}"
            else:
                # 尝试从计数器名称中提取ID
                counter_id = extract_counter_id_from_name(counter_ref)
                if counter_id in counter_map:
                    parsed_condition = f"row['{counter_map[counter_id]}'] {operator} {value}"
                else:
                    all_counters_found = False
                    parsed_condition = f"False"  # 计数器不存在时返回False
        else:
            # 未知的条件格式
            parsed_condition = "False"
            all_counters_found = False
    else:
        # 处理旧格式的条件（字符串格式）
        parsed_condition = str(condition)
        
        # 确保条件中的=被替换为==（除非已经是==、>=、<=）
        if '==' not in parsed_condition and '>=' not in parsed_condition and '<=' not in parsed_condition:
            parsed_condition = parsed_condition.replace('=', '==')

        # Find all counter IDs in the condition
        counter_ids = re.findall(r'[A-Za-z0-9]+(?:\[\d+\])?', parsed_condition)

        for counter_id in counter_ids:
            if counter_id in counter_map:
                # Replace counter ID with its column name
                parsed_condition = parsed_condition.replace(
                    counter_id, f"row['{counter_map[counter_id]}']"
                )
            else:
                # Skip common words that might be mistaken for counter IDs
                if counter_id.lower() in ['and', 'or', 'not', 'true', 'false', 'none']:
                    continue

                # Skip numeric values
                if counter_id.isdigit():
                    continue

                # If counter not found in data, mark as missing
                all_counters_found = False

    return parsed_condition, all_counters_found


def evaluate_condition(condition: str, row: pd.Series) -> bool:
    """评估条件"""
    if not condition or not condition.strip():
        raise ValueError("Condition cannot be empty")

    # Create a copy of the row to avoid modifying the original
    row_copy = row.copy()

    # Handle percentage values in the data
    for col in row_copy.index:
        if isinstance(row_copy[col], str) and '%' in row_copy[col]:
            # Convert string percentages to decimal values (e.g., "66.11%" -> 0.6611)
            row_copy[col] = float(row_copy[col].replace('%', '')) / 100
        elif pd.isna(row_copy[col]):
            # Replace NaN values with 0 to avoid evaluation errors
            row_copy[col] = 0

    # Evaluate the condition using the modified row
    result = eval(condition, {"__builtins__": {}}, {"row": row_copy, "np": np})
    return bool(result)


def get_validation_result_columns(df: pd.DataFrame) -> List[str]:
    """获取校验结果页面应该包含的列（公共数据字段和第一个合并文件的第一个小区字段）"""
    selected_columns = []

    # 1. 添加基础字段（公共数据字段）
    for base_field in BASE_FIELDS:
        if base_field in df.columns:
            selected_columns.append(base_field)
            print(f"  添加基础字段: {base_field}")

    # 2. 添加第一个合并文件的第一个小区字段
    # 注意：这里的逻辑是找到数据中第一个出现的小区字段
    # 在合并后的数据中，列的顺序通常反映了合并的顺序
    first_cell_field = None

    # 按照数据框中列的实际顺序查找第一个小区字段
    for col in df.columns:
        # 检查这个列是否是小区字段
        is_cell_field = False
        for field_variants in FIELD_MAPPINGS['CELL_FIELDS'].values():
            if col in field_variants:
                is_cell_field = True
                break

        if is_cell_field:
            first_cell_field = col
            break

    if first_cell_field:
        selected_columns.append(first_cell_field)
        print(f"  添加第一个合并文件的第一个小区字段: {first_cell_field}")
    else:
        print(f"  警告: 未找到任何小区字段")

    # 3. 不添加数据源字段（按要求完全删除）
    if '数据源' in df.columns:
        print(f"  跳过数据源字段: 按要求不包含数据源字段")

    print(f"校验结果页面将包含 {len(selected_columns)} 个字段: {selected_columns}")
    return selected_columns


def filter_rules_by_counters(rules: List[Dict[str, Any]], available_counters: set) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """根据可用计数器筛选规则"""
    valid_rules = []
    discarded_rules = []

    for rule in rules:
        rule_id = rule['rule_id']
        rule_counter_names = rule['counters']

        # 从计数器名称中提取计数器ID
        rule_counter_ids = set()
        for counter_name in rule_counter_names:
            counter_id = extract_counter_id_from_name(counter_name)
            rule_counter_ids.add(counter_id)

        # Check if all counters required by the rule are available
        missing_counter_ids = rule_counter_ids - available_counters

        if missing_counter_ids:
            # Some counters are missing, discard the rule
            discarded_rule = rule.copy()
            # 找出缺失的计数器名称
            missing_counter_names = []
            for counter_name in rule_counter_names:
                counter_id = extract_counter_id_from_name(counter_name)
                if counter_id in missing_counter_ids:
                    missing_counter_names.append(counter_name)
            discarded_rule['missing_counters'] = missing_counter_names
            discarded_rules.append(discarded_rule)
        else:
            # All counters are available, keep the rule
            valid_rules.append(rule)

    return valid_rules, discarded_rules


def validate_single_row_rule(row, rule, counter_map) -> str:
    """验证单行数据对单个规则的结果"""
    rule_id = rule['rule_id']

    # 跳过空规则
    if not rule['counters'] and not rule['necessary_conditions'] and not rule['judgment_conditions']:
        return "跳过"

    # 检查是否所有必需的计数器都可用
    missing_counters = []
    for counter_name in rule['counters']:
        counter_id = extract_counter_id_from_name(counter_name)
        if counter_id not in counter_map:
            missing_counters.append(counter_name)

    if missing_counters:
        return "跳过"

    # 获取百分比计数器ID列表
    percentage_counters = set()
    for counter_name in rule['counters']:
        if '%' in counter_name:
            counter_id = extract_counter_id_from_name(counter_name)
            percentage_counters.add(counter_id)

    # 评估必要条件
    necessary_conditions_met = True
    for condition in rule['necessary_conditions']:
        # 处理百分比值
        if isinstance(condition, dict) and 'counter' in condition and 'value' in condition:
            counter_ref = condition['counter']
            if counter_ref in percentage_counters:
                # 如果是百分比计数器，将值转换为小数
                condition = condition.copy()  # 创建副本以避免修改原始规则
                condition['value'] = condition['value'] / 100

        parsed_condition, all_counters_found = parse_condition(condition, counter_map)
        if not all_counters_found or not evaluate_condition(parsed_condition, row):
            necessary_conditions_met = False
            break

    if not necessary_conditions_met:
        return "失败"

    # 评估判决条件
    judgment_conditions_met = True
    for condition in rule['judgment_conditions']:
        # 处理百分比值
        if isinstance(condition, dict) and 'counter' in condition and 'value' in condition:
            counter_ref = condition['counter']
            if counter_ref in percentage_counters:
                # 如果是百分比计数器，将值转换为小数
                condition = condition.copy()  # 创建副本以避免修改原始规则
                condition['value'] = condition['value'] / 100

        parsed_condition, all_counters_found = parse_condition(condition, counter_map)
        if not all_counters_found or not evaluate_condition(parsed_condition, row):
            judgment_conditions_met = False
            break

    if judgment_conditions_met:
        return "通过"
    else:
        return "失败"


def validate_data(df: pd.DataFrame, rules: List[Dict[str, Any]]) -> pd.DataFrame:
    """验证数据"""
    # 检查数据是否为空
    if df.empty:
        print("WARNING: The input data has no rows. Skipping validation.")
        return pd.DataFrame()

    # Get available counters in the data
    counter_map = get_available_counters(df)

    # 获取公共数据字段和小区字段（来自第一个合并文件）
    selected_columns = get_validation_result_columns(df)

    # 创建结果DataFrame，只复制选定的字段
    result_df = df[selected_columns].copy()

    # 为每个规则添加一列
    rule_columns = []
    for rule in rules:
        rule_id = rule['rule_id']
        rule_column = f"Rule{rule_id}"
        rule_columns.append(rule_column)
        result_df[rule_column] = "未验证"  # 默认值

    # 添加总校验结果列
    result_df["总校验结果"] = "未验证"

    # 直接处理所有行
    for idx, row in df.iterrows():
        # 为每个规则验证这一行
        for rule in rules:
            rule_id = rule['rule_id']
            rule_column = f"Rule{rule_id}"
            validation_result = validate_single_row_rule(row, rule, counter_map)
            result_df.loc[idx, rule_column] = validation_result

        # 计算总校验结果
        overall_result = calculate_overall_result(result_df.loc[idx], rule_columns)
        result_df.loc[idx, "总校验结果"] = overall_result

    return result_df


def calculate_overall_result(row_data, rule_columns) -> str:
    """计算总校验结果"""
    rule_results = []
    for rule_col in rule_columns:
        if rule_col in row_data:
            rule_results.append(row_data[rule_col])

    # 如果没有规则结果，返回"跳过"
    if not rule_results:
        return "跳过"

    # 统计各种结果
    passed_count = rule_results.count("通过")
    failed_count = rule_results.count("失败")
    skipped_count = rule_results.count("跳过")

    # 如果所有规则都被跳过，总结果为"跳过"
    if skipped_count == len(rule_results):
        return "跳过"

    # 如果有任何规则失败，总结果为"失败"
    if failed_count > 0:
        return "失败"

    # 如果有通过的规则且没有失败的规则，总结果为"成功"
    if passed_count > 0:
        return "成功"

    # 其他情况
    return "未知"


def load_excel_data(excel_file: str) -> pd.DataFrame:
    """加载Excel数据"""
    if not os.path.exists(excel_file):
        raise FileNotFoundError(f"Excel file not found: {excel_file}")

    # Load Sheet0 which contains the counter data
    df = pd.read_excel(excel_file, sheet_name='Sheet0')

    # 检查Sheet0是否为空或只有表头没有数据行
    if df.empty:
        print(f"INFO: Sheet0 in {excel_file} has no data rows - skipping processing")

    return df


def load_counter_sheet(excel_file: str) -> set:
    """从计数器表中加载计数器"""
    if not os.path.exists(excel_file):
        raise FileNotFoundError(f"Excel file not found: {excel_file}")

    # Check for possible counter sheet names
    xl = pd.ExcelFile(excel_file)
    counter_sheet_name = None
    possible_sheet_names = ["指标(计数器)", "KPI(Counter)"]

    for sheet_name in possible_sheet_names:
        if sheet_name in xl.sheet_names:
            counter_sheet_name = sheet_name
            break

    if counter_sheet_name is None:
        print(f"WARNING: No counter sheet found in {excel_file}")
        return set()

    # Load the counter sheet
    counter_df = pd.read_excel(excel_file, sheet_name=counter_sheet_name)

    # Check if the sheet is empty
    if counter_df.empty:
        print(f"WARNING: '{counter_sheet_name}' sheet in {excel_file} is empty")
        return set()

    # Find the column containing counter IDs
    counter_id_col = None
    for col in counter_df.columns:
        col_str = str(col).lower()
        if any(keyword.lower() in col_str for keyword in COUNTER_ID_KEYWORDS):
            counter_id_col = col
            break

    if counter_id_col is None:
        print(f"WARNING: Could not identify counter ID column in '{counter_sheet_name}' sheet")
        return set()

    # Extract counter IDs
    counter_ids = set()
    for counter_id in counter_df[counter_id_col]:
        if pd.notna(counter_id):
            # Extract the base counter ID (remove any brackets and text after colon)
            counter_id_str = str(counter_id).strip()
            # Handle counters with brackets like "C600000001[9]"
            bracket_match = re.match(r'^([A-Za-z0-9]+)(\[\d+\])?', counter_id_str)
            if bracket_match:
                if bracket_match.group(2):  # If there's a bracket part
                    counter_ids.add(bracket_match.group(1) + bracket_match.group(2))
                else:
                    counter_ids.add(bracket_match.group(1))
            else:
                # For other formats, just add the ID
                counter_ids.add(counter_id_str)

    print(f"Loaded {len(counter_ids)} counters from '{counter_sheet_name}' sheet")
    return counter_ids


def validate_single_file(excel_file: str):
    """对单个文件进行KPI验证"""
    print(f"Loading built-in validation rules...")
    rules = load_rules()

    print(f"Loaded {len(rules)} rules")

    # Load counters from counter sheet
    print(f"Checking available counters in counter sheet...")
    available_counters = load_counter_sheet(excel_file)

    # Filter rules based on available counters
    discarded_rules = []
    if available_counters:
        print(f"Filtering rules based on {len(available_counters)} available counters")
        valid_rules, discarded_rules = filter_rules_by_counters(rules, available_counters)
        print(f"Selected {len(valid_rules)} rules for validation, discarded {len(discarded_rules)} rules")

        # If all rules are discarded, still create Summary but skip validation
        if not valid_rules:
            print("WARNING: All rules have been discarded due to missing counters")
            create_summary_only(excel_file, discarded_rules)
            return
    else:
        # No counters found in counter sheet, use all rules
        print("WARNING: No counters found in counter sheet. Using all rules")
        valid_rules = rules

    print(f"Loading data from {excel_file}...")
    df = load_excel_data(excel_file)
    print(f"Loaded data shape: {df.shape}")

    # 检查数据是否为空或只有表头
    if df.empty:
        print("INFO: Sheet0 has no data rows - skipping all validation processing")
        return

    print("Validating data...")
    results_df = validate_data(df, valid_rules)

    if results_df.empty:
        print("No validation results generated")
        return

    # 将结果写入原文件的新 sheet 页
    print(f"Adding validation results to {excel_file}...")
    write_validation_results(excel_file, results_df, valid_rules, discarded_rules)

    # 打印摘要
    total_rows = len(results_df)
    rule_columns = [col for col in results_df.columns if col.startswith('Rule')]

    print(f"Validation Summary: {total_rows} rows, {len(rule_columns)} rules validated, {len(valid_rules)} matched, {len(discarded_rules)} discarded")

    # 统计总校验结果
    if "总校验结果" in results_df.columns:
        overall_results = results_df["总校验结果"].value_counts()
        print(f"Overall results: {dict(overall_results)}")


def create_summary_only(excel_file: str, discarded_rules: List[Dict]):
    """仅创建摘要信息"""
    from openpyxl import load_workbook
    from openpyxl.utils.dataframe import dataframe_to_rows

    # Create Summary DataFrame for discarded rules only
    summary_data = []
    for rule in discarded_rules:
        summary_data.append({
            'Rule ID': f"Rule {rule['rule_id']}",
            'Rule Description': rule['description'],
            'Status': '未匹配',
            'Required Counters': ', '.join(rule['counters']),
            'Missing Counters': ', '.join(rule['missing_counters']),
            'Reason': f"缺少计数器: {', '.join(rule['missing_counters'])}"
        })

    summary_df = pd.DataFrame(summary_data)

    # Write Summary to Excel file
    wb = load_workbook(excel_file)

    # Check and remove existing Summary sheet
    if 'Summary' in wb.sheetnames:
        wb.remove(wb['Summary'])

    # Create new Summary sheet
    ws_summary = wb.create_sheet('Summary')

    # Write Summary data
    for r in dataframe_to_rows(summary_df, index=False, header=True):
        ws_summary.append(r)

    wb.save(excel_file)
    print(f"Summary information added to 'Summary' sheet in {excel_file}")
    print(f"Summary: Rules matched: 0, Rules discarded: {len(discarded_rules)}")


def write_validation_results(excel_file: str, results_df: pd.DataFrame,
                           valid_rules: List[Dict], discarded_rules: List[Dict]):
    """将校验结果写入Excel文件"""
    from openpyxl import load_workbook
    from openpyxl.utils.dataframe import dataframe_to_rows

    # 加载现有的工作簿
    wb = load_workbook(excel_file)

    # 检查是否已存在"校验结果" sheet，如果存在则删除
    if '校验结果' in wb.sheetnames:
        wb.remove(wb['校验结果'])

    # 创建新的"校验结果" sheet
    ws_results = wb.create_sheet('校验结果')

    # 将校验结果数据写入 sheet
    for r in dataframe_to_rows(results_df, index=False, header=True):
        ws_results.append(r)

    # 创建Summary DataFrame
    summary_data = []

    # 添加匹配的规则
    for rule in valid_rules:
        summary_data.append({
            'Rule ID': f"Rule {rule['rule_id']}",
            'Rule Description': rule['description'],
            'Status': '已匹配',
            'Required Counters': ', '.join(rule['counters']),
            'Missing Counters': '',
            'Reason': '所有必需计数器都可用'
        })

    # 添加未匹配的规则
    for rule in discarded_rules:
        summary_data.append({
            'Rule ID': f"Rule {rule['rule_id']}",
            'Rule Description': rule['description'],
            'Status': '未匹配',
            'Required Counters': ', '.join(rule['counters']),
            'Missing Counters': ', '.join(rule['missing_counters']),
            'Reason': f"缺少计数器: {', '.join(rule['missing_counters'])}"
        })

    # 创建Summary DataFrame
    summary_df = pd.DataFrame(summary_data)

    # 检查是否已存在"Summary" sheet，如果存在则删除
    if 'Summary' in wb.sheetnames:
        wb.remove(wb['Summary'])

    # 创建新的"Summary" sheet
    ws_summary = wb.create_sheet('Summary')

    # 将Summary数据写入 sheet
    for r in dataframe_to_rows(summary_df, index=False, header=True):
        ws_summary.append(r)

    # 保存文件
    wb.save(excel_file)

    print(f"Validation results added to '校验结果' sheet in {excel_file}")
    print(f"Summary information added to 'Summary' sheet in {excel_file}")


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python kpi_validation_integrated.py <directory_path>")
        print("  示例: python kpi_validation_integrated.py /path/to/excel/files")
        print("  目录应包含Excel文件 (.xlsx, .xls, .xlsm)")
        print("  校验规则已内置在脚本中")
        sys.exit(1)

    directory_path = sys.argv[1]

    # 扫描目录中的Excel文件
    excel_files = scan_excel_files(directory_path)

    if not excel_files:
        print(f"ERROR: No Excel files found in directory: {directory_path}")
        sys.exit(1)

    print(f"目录模式: 找到 {len(excel_files)} 个Excel文件")

    # 直接处理KPI文件（不分组，使用KPI合并工具的原始逻辑）
    processed_files = process_kpi_files_directly(excel_files)

    if not processed_files:
        print("ERROR: No valid files found for processing")
        sys.exit(1)

    # 对每个处理后的文件进行校验
    for i, file_path in enumerate(processed_files):
        print(f"\n处理文件 {i+1}/{len(processed_files)}: {os.path.basename(file_path)}")
        validate_single_file(file_path)

    print(f"所有文件处理完成！")


if __name__ == "__main__":
    main()


