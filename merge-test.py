import pandas as pd
import os,copy

common_columns = [
        '开始时间', '结束时间', '粒度', '子网ID', '子网名称', 
        '网元ID', '管理网元'
    ]
special_fields = ["CU小区配置ID",'DU物理小区ID',"NR载波ID", "NRCarrierId",'DU小区配置ID']

def exclude_file(file_name: str):
    df = pd.read_excel(file_name)
    for item in ["GNB", "Replaceable", "SSBBeam", "results", "drop","可替换单元ID","波束信息ID"]:
        if item in file_name:
            return True
    return False

def contains_element(df):
    for special_field in special_fields:
        if special_field in df.columns:
                return special_field
    return None

def merge_files(file_list, output_path='merged.xlsx'):
    all_data = []
    """ files_name = []
    for filename in os.listdir(folder_path):
        if filename.endswith((".xls", ".xlsx")) and not filename.startswith("~$"):  # 过滤掉临时文件
            file_path = os.path.join(folder_path, filename)
            if exclude_file(filename):
                print(f'exclude_file{filename}')
                continue
            try:
                # 读取Excel文件
                df = pd.read_excel(file_path)
                if len(df):
                    all_data.append(df)
                    files_name.append(filename)
            except Exception as e:
                print(f"读取文件 {filename} 时发生错误: {e}")

    if not all_data:
        print("没有找到任何Excel文件或无法读取任何文件。")
        return """
    for file in file_list:
        all_data.append(pd.read_excel(file))

    
    merged_df = all_data[0]
    
    for i in range(1, len(all_data)):
        Intersection_key = set(merged_df.columns) & set(all_data[i].columns)
        """ left_on = copy.deepcopy(common_columns)
        right_on = copy.deepcopy(common_columns) """
        left_on = copy.deepcopy(list(Intersection_key))
        right_on = copy.deepcopy(list(Intersection_key))
        left_special_field =  contains_element(merged_df)
        right_special_field = contains_element(all_data[i])
        left_on.append(left_special_field)
        right_on.append(right_special_field)
        

        # 合并所有数据
        merged_df = pd.merge(merged_df, all_data[i],
                             left_on = left_on,
                             right_on = right_on
                             )
    # 写入Excel文件
    merged_df.to_excel(output_path, index=False)
    print(f"合并完成，结果保存至：{output_path}")

# 使用示例
if __name__ == "__main__":
    excel_folder = r"D:\部门工作\AI提效\性能数据分析\KPI文件\202505061414256889\kpi\raw"
    files = [
        "D:\\部门工作\\AI提效\\性能数据分析\\KPI文件\\202505061414256889\\kpi\\raw\\性能管理-历史查询-21078-系统性能模板-PLMNDU-202505061400-202505061405.xlsx",
        "D:\\部门工作\\AI提效\\性能数据分析\\KPI文件\\202505061414256889\\kpi\\raw\\性能管理-历史查询-21078-系统性能模板-PLMNCUUP-202505061400-202505061405.xlsx",
        "D:\\部门工作\\AI提效\\性能数据分析\\KPI文件\\202505061414256889\\kpi\\raw\\性能管理-历史查询-21078-系统性能模板-PHYSICALDU1-202505061400-202505061405.xlsx",
        "D:\\部门工作\\AI提效\\性能数据分析\\KPI文件\\202505061414256889\\kpi\\raw\\性能管理-历史查询-21078-系统性能模板-载波-202505061400-202505061405.xlsx"
    ]
    merge_files(files, 'merged_output.xlsx')