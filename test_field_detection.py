#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试字段检测功能
"""

import pandas as pd
import os

# 模拟一些测试数据
def create_test_excel_files():
    """创建测试Excel文件"""

    # 测试文件1：包含CU小区配置ID
    data1 = {
        '开始时间': ['2024-01-01 00:00:00', '2024-01-01 00:15:00'],
        '结束时间': ['2024-01-01 00:15:00', '2024-01-01 00:30:00'],
        '粒度': ['15分钟', '15分钟'],
        'SubnetWork ID': ['subnet1', 'subnet1'],
        'CU小区配置ID': ['cell001', 'cell002'],
        '668166 小区下行PDCP层平均流量(Mbps)': [100.5, 120.3],
        '668002 小区下行 PDCP SDU弃包率(%)': [0.1, 0.2]
    }
    # 创建文件1（包含Sheet0和计数器定义页面）
    df1 = pd.DataFrame(data1)
    with pd.ExcelWriter('test_file1.xlsx', engine='openpyxl') as writer:
        df1.to_excel(writer, sheet_name='Sheet0', index=False)
        # 添加计数器定义页面
        counter_data1 = {
            '指标或计数器': ['668166', '668002', '668167'],
            '指标名称': ['小区下行PDCP层平均流量', '小区下行PDCP SDU弃包率', '小区上行PDCP层平均流量'],
            '单位': ['Mbps', '%', 'Mbps'],
            '来源文件': ['文件1', '文件1', '文件1']
        }
        pd.DataFrame(counter_data1).to_excel(writer, sheet_name='指标(计数器)', index=False)

    # 测试文件2：包含nrPhysicalCellDUId
    data2 = {
        'Start Time': ['2024-01-01 00:00:00', '2024-01-01 00:15:00'],
        'End Time': ['2024-01-01 00:15:00', '2024-01-01 00:30:00'],
        'Granularity Period': ['15MIN', '15MIN'],
        'Subnetwork ID': ['subnet1', 'subnet1'],
        'nrPhysicalCellDUId': ['du001', 'du002'],
        '668167 小区上行PDCP层平均流量(Mbps)': [80.5, 90.3],
        '668000 小区上行PDCP SDU丢包率(%)': [0.05, 0.08]
    }
    df2 = pd.DataFrame(data2)
    with pd.ExcelWriter('test_file2.xlsx', engine='openpyxl') as writer:
        df2.to_excel(writer, sheet_name='Sheet0', index=False)
        # 添加计数器定义页面（英文版本）
        counter_data2 = {
            'kpi or counter': ['668000', '670138', '670033'],
            'Counter Name': ['小区上行PDCP SDU丢包率', '小区上行BLER', '小区上行HARQ失败比例'],
            'Unit': ['%', '%', '%'],
            'Source File': ['文件2', '文件2', '文件2']
        }
        pd.DataFrame(counter_data2).to_excel(writer, sheet_name='KPI(Counter)', index=False)

    # 测试文件3：包含其他类型的小区字段
    data3 = {
        '开始时间': ['2024-01-01 00:00:00', '2024-01-01 00:15:00'],
        '结束时间': ['2024-01-01 00:15:00', '2024-01-01 00:30:00'],
        '粒度': ['15分钟', '15分钟'],
        'SubnetWork ID': ['subnet1', 'subnet1'],
        'PhysicalCellId': ['pci001', 'pci002'],  # 不同的小区字段类型
        '670138 小区上行BLER(%)': [5.2, 4.8],
        '670033 小区上行HARQ失败比例(%)': [0.3, 0.4]
    }
    df3 = pd.DataFrame(data3)
    # 文件3只有Sheet0，没有计数器定义页面（测试混合情况）
    df3.to_excel('test_file3.xlsx', sheet_name='Sheet0', index=False)

    print("创建了3个测试Excel文件:")
    print("  - test_file1.xlsx (包含CU小区配置ID + 指标(计数器)页面)")
    print("  - test_file2.xlsx (包含nrPhysicalCellDUId + KPI(Counter)页面)")
    print("  - test_file3.xlsx (包含PhysicalCellId，仅Sheet0)")

def test_field_detection():
    """测试字段检测功能"""
    print("\n测试字段检测功能...")

    # 导入必要的函数
    from kpi_validation_integrated import detect_kpi_fields, FIELD_MAPPINGS

    test_files = ['test_file1.xlsx', 'test_file2.xlsx', 'test_file3.xlsx']

    try:
        result = detect_kpi_fields(test_files)
        print(f"\n字段检测结果:")
        for field_type, fields in result.items():
            print(f"  {field_type}: {fields}")

        print("\n✅ 字段检测成功！")
        return True

    except Exception as e:
        print(f"\n❌ 字段检测失败: {str(e)}")
        return False

def test_file_filtering():
    """测试文件筛选功能"""
    print("\n测试文件筛选功能...")

    from kpi_validation_integrated import filter_valid_kpi_files

    test_files = ['test_file1.xlsx', 'test_file2.xlsx', 'test_file3.xlsx']

    try:
        valid_files = filter_valid_kpi_files(test_files)
        print(f"\n筛选结果:")
        for file_info in valid_files:
            print(f"  ✓ {file_info['file']} - 字段: {file_info['cell_fields']}")

        print(f"\n✅ 文件筛选成功！找到 {len(valid_files)} 个有效文件")
        return True

    except Exception as e:
        print(f"\n❌ 文件筛选失败: {str(e)}")
        return False

def test_direct_processing():
    """测试直接处理功能"""
    print("\n测试直接处理功能...")

    from kpi_validation_integrated import process_kpi_files_directly

    test_files = ['test_file1.xlsx', 'test_file2.xlsx', 'test_file3.xlsx']

    try:
        processed_files = process_kpi_files_directly(test_files)
        print(f"\n处理结果:")
        for file in processed_files:
            print(f"  ✓ {file}")

        print(f"\n✅ 直接处理成功！生成 {len(processed_files)} 个文件")
        return True

    except Exception as e:
        print(f"\n❌ 直接处理失败: {str(e)}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    test_files = ['test_file1.xlsx', 'test_file2.xlsx', 'test_file3.xlsx']
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"删除测试文件: {file}")

def main():
    """主测试函数"""
    print("KPI字段检测测试")
    print("=" * 50)

    # 1. 创建测试文件
    create_test_excel_files()

    try:
        # 2. 测试字段检测
        success1 = test_field_detection()

        # 3. 测试文件筛选
        success2 = test_file_filtering()

        # 4. 测试直接处理
        success3 = test_direct_processing()

        # 5. 总结
        print("\n" + "=" * 50)
        print("测试总结:")
        print(f"  字段检测: {'✅ 成功' if success1 else '❌ 失败'}")
        print(f"  文件筛选: {'✅ 成功' if success2 else '❌ 失败'}")
        print(f"  直接处理: {'✅ 成功' if success3 else '❌ 失败'}")

        if all([success1, success2, success3]):
            print("\n🎉 所有测试通过！")
        else:
            print("\n⚠️ 部分测试失败，请检查错误信息")

    finally:
        # 6. 清理测试文件
        print("\n清理测试文件...")
        cleanup_test_files()

if __name__ == "__main__":
    main()
