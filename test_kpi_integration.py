#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
KPI集成功能测试脚本

用于测试新集成的KPI数据校验工具的功能
"""

import os
import sys
import pandas as pd
from kpi_validation_integrated import (
    scan_excel_files,
    detect_kpi_fields,
    filter_valid_kpi_files,
    process_kpi_files_directly,
    merge_kpi_files_advanced,
    validate_single_file
)

def test_scan_files():
    """测试文件扫描功能"""
    print("=" * 50)
    print("测试文件扫描功能")
    print("=" * 50)

    current_dir = os.getcwd()
    print(f"当前目录: {current_dir}")

    try:
        excel_files = scan_excel_files(current_dir)
        print(f"找到的Excel文件:")
        for i, file in enumerate(excel_files, 1):
            print(f"  {i}. {os.path.basename(file)}")
        return excel_files
    except Exception as e:
        print(f"文件扫描失败: {str(e)}")
        return []

def test_field_detection(excel_files):
    """测试字段检测功能"""
    print("\n" + "=" * 50)
    print("测试字段检测功能")
    print("=" * 50)

    if not excel_files:
        print("没有Excel文件可供测试")
        return

    try:
        detected_fields = detect_kpi_fields(excel_files)
        print("检测到的字段:")
        for field_type, fields in detected_fields.items():
            print(f"  {field_type}: {fields}")
    except Exception as e:
        print(f"字段检测失败: {str(e)}")

def test_file_filtering(excel_files):
    """测试文件筛选功能"""
    print("\n" + "=" * 50)
    print("测试文件筛选功能")
    print("=" * 50)

    if not excel_files:
        print("没有Excel文件可供测试")
        return []

    try:
        valid_files = filter_valid_kpi_files(excel_files)
        print(f"有效的KPI文件:")
        for i, file_info in enumerate(valid_files, 1):
            print(f"  {i}. {os.path.basename(file_info['file'])} - {file_info['rows']} 行")
        return valid_files
    except Exception as e:
        print(f"文件筛选失败: {str(e)}")
        return []

def test_direct_processing(excel_files):
    """测试直接处理功能（不分组）"""
    print("\n" + "=" * 50)
    print("测试直接处理功能")
    print("=" * 50)

    if not excel_files:
        print("没有Excel文件可供测试")
        return []

    try:
        processed_files = process_kpi_files_directly(excel_files)
        print(f"直接处理结果:")
        for i, file in enumerate(processed_files, 1):
            print(f"  {i}. {os.path.basename(file)}")
        return processed_files
    except Exception as e:
        print(f"直接处理失败: {str(e)}")
        return []

def test_single_file_validation(processed_files):
    """测试单文件校验功能"""
    print("\n" + "=" * 50)
    print("测试单文件校验功能")
    print("=" * 50)

    if not processed_files:
        print("没有处理后的文件可供测试")
        return

    # 只测试第一个文件
    test_file = processed_files[0]
    print(f"测试文件: {os.path.basename(test_file)}")

    try:
        validate_single_file(test_file)
        print("单文件校验测试完成")
    except Exception as e:
        print(f"单文件校验失败: {str(e)}")

def test_merge_functionality(excel_files):
    """测试合并功能"""
    print("\n" + "=" * 50)
    print("测试合并功能")
    print("=" * 50)

    if not excel_files or len(excel_files) < 2:
        print("需要至少2个Excel文件才能测试合并功能")
        return

    print(f"测试合并 {len(excel_files)} 个文件:")
    for file in excel_files:
        print(f"  - {os.path.basename(file)}")

    try:
        output_file = "test_merged_output.xlsx"
        merged_file = merge_kpi_files_advanced(excel_files, output_file)
        print(f"合并测试完成: {merged_file}")

        # 检查合并后的文件
        if os.path.exists(merged_file):
            df = pd.read_excel(merged_file, sheet_name='Sheet0')
            print(f"合并后文件信息: {len(df)} 行, {len(df.columns)} 列")

    except Exception as e:
        print(f"合并功能测试失败: {str(e)}")

def main():
    """主测试函数"""
    print("KPI集成功能测试开始")
    print("=" * 50)

    # 1. 测试文件扫描
    excel_files = test_scan_files()

    if not excel_files:
        print("没有找到Excel文件，测试结束")
        return

    # 2. 测试字段检测
    test_field_detection(excel_files)

    # 3. 测试文件筛选
    valid_files = test_file_filtering(excel_files)

    # 4. 测试直接处理（不分组）
    processed_files = test_direct_processing(excel_files)

    # 5. 测试合并功能
    test_merge_functionality(excel_files)

    # 6. 测试单文件校验
    test_single_file_validation(processed_files)

    print("\n" + "=" * 50)
    print("KPI集成功能测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
