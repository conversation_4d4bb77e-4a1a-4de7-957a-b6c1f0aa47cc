#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试校验结果页面字段选择功能
"""

import pandas as pd
import os

def test_validation_columns():
    """测试校验结果页面的字段选择功能"""
    print("测试校验结果页面字段选择功能")
    print("=" * 50)

    # 导入必要的函数
    from kpi_validation_integrated import get_validation_result_columns, BASE_FIELDS, FIELD_MAPPINGS

    # 测试场景1：模拟第一个合并文件有多个小区字段的情况
    print("\n场景1：第一个合并文件有多个小区字段（应该取第一个）")
    test_data1 = {
        # 基础字段（公共数据字段）
        '开始时间': ['2024-01-01 00:00:00', '2024-01-01 00:15:00'],
        '结束时间': ['2024-01-01 00:15:00', '2024-01-01 00:30:00'],
        '粒度': ['15分钟', '15分钟'],
        'SubnetWork ID': ['subnet1', 'subnet1'],

        # 模拟第一个合并文件的多个小区字段（按列顺序，CU小区配置ID是第一个）
        'CU小区配置ID': ['cell001', 'cell002'],
        'nrPhysicalCellDUId': ['du001', 'du002'],
        'PhysicalCellId': ['pci001', 'pci002'],

        # 数据源字段（应该被完全去除）
        '数据源': ['test_file1.xlsx', 'test_file1.xlsx'],

        # 计数器字段（这些不应该出现在校验结果中）
        '668166:小区下行PDCP层平均流量(Mbps)': [100.5, 120.3],
        '668002:小区下行PDCP SDU弃包率(%)': [0.1, 0.2],

        # 其他非基础字段（这些也不应该出现在校验结果中）
        '其他字段1': ['value1', 'value2'],
        '其他字段2': ['value3', 'value4']
    }

    # 测试场景2：模拟第一个合并文件的小区字段是nrPhysicalCellDUId的情况
    print("\n场景2：第一个合并文件的第一个小区字段是nrPhysicalCellDUId")
    test_data2 = {
        # 基础字段（公共数据字段）
        '开始时间': ['2024-01-01 00:00:00', '2024-01-01 00:15:00'],
        '结束时间': ['2024-01-01 00:15:00', '2024-01-01 00:30:00'],
        '粒度': ['15分钟', '15分钟'],
        'SubnetWork ID': ['subnet1', 'subnet1'],

        # 模拟第一个合并文件的第一个小区字段是nrPhysicalCellDUId（按列顺序）
        'nrPhysicalCellDUId': ['du001', 'du002'],
        'CU小区配置ID': ['cell001', 'cell002'],  # 这个是第二个，不应该被选中

        # 数据源字段（应该被完全去除）
        '数据源': ['test_file1.xlsx', 'test_file2.xlsx'],  # 即使有多个数据源也要去除

        # 计数器字段
        '668166:小区下行PDCP层平均流量(Mbps)': [100.5, 120.3],
        '668002:小区下行PDCP SDU弃包率(%)': [0.1, 0.2]
    }

    # 测试场景1
    df1 = pd.DataFrame(test_data1)
    print(f"场景1 - 原始数据包含 {len(df1.columns)} 个字段:")
    for i, col in enumerate(df1.columns, 1):
        print(f"  {i:2d}. {col}")

    print(f"执行字段选择...")
    selected_columns1 = get_validation_result_columns(df1)

    # 测试场景2
    df2 = pd.DataFrame(test_data2)
    print(f"\n场景2 - 原始数据包含 {len(df2.columns)} 个字段:")
    for i, col in enumerate(df2.columns, 1):
        print(f"  {i:2d}. {col}")

    print(f"执行字段选择...")
    selected_columns2 = get_validation_result_columns(df2)

    # 分析结果
    print(f"\n=== 结果分析 ===")

    print(f"\n场景1分析（第一个小区字段是CU小区配置ID）:")
    print(f"  原始字段数: {len(df1.columns)}")
    print(f"  选择字段数: {len(selected_columns1)}")
    print(f"  选择的字段: {selected_columns1}")

    # 检查是否包含数据源字段（应该不包含）
    has_data_source1 = '数据源' in selected_columns1
    print(f"  包含数据源字段: {'是' if has_data_source1 else '否'} ({'❌ 错误' if has_data_source1 else '✅ 正确'})")

    # 检查小区字段
    cell_fields_in_result1 = [col for col in selected_columns1 if any(col in variants for variants in FIELD_MAPPINGS['CELL_FIELDS'].values())]
    print(f"  小区字段: {cell_fields_in_result1}")
    print(f"  小区字段数量: {len(cell_fields_in_result1)} ({'✅ 正确' if len(cell_fields_in_result1) == 1 else '❌ 错误'})")

    # 检查是否选择了第一个小区字段
    expected_first_cell1 = 'CU小区配置ID'  # 在场景1中，这是第一个小区字段
    actual_first_cell1 = cell_fields_in_result1[0] if cell_fields_in_result1 else None
    print(f"  期望的第一个小区字段: {expected_first_cell1}")
    print(f"  实际选择的小区字段: {actual_first_cell1} ({'✅ 正确' if actual_first_cell1 == expected_first_cell1 else '❌ 错误'})")

    print(f"\n场景2分析（第一个小区字段是nrPhysicalCellDUId）:")
    print(f"  原始字段数: {len(df2.columns)}")
    print(f"  选择字段数: {len(selected_columns2)}")
    print(f"  选择的字段: {selected_columns2}")

    # 检查是否包含数据源字段（应该不包含）
    has_data_source2 = '数据源' in selected_columns2
    print(f"  包含数据源字段: {'是' if has_data_source2 else '否'} ({'❌ 错误' if has_data_source2 else '✅ 正确'})")

    # 检查小区字段
    cell_fields_in_result2 = [col for col in selected_columns2 if any(col in variants for variants in FIELD_MAPPINGS['CELL_FIELDS'].values())]
    print(f"  小区字段: {cell_fields_in_result2}")
    print(f"  小区字段数量: {len(cell_fields_in_result2)} ({'✅ 正确' if len(cell_fields_in_result2) == 1 else '❌ 错误'})")

    # 检查是否选择了第一个小区字段
    expected_first_cell2 = 'nrPhysicalCellDUId'  # 在场景2中，这是第一个小区字段
    actual_first_cell2 = cell_fields_in_result2[0] if cell_fields_in_result2 else None
    print(f"  期望的第一个小区字段: {expected_first_cell2}")
    print(f"  实际选择的小区字段: {actual_first_cell2} ({'✅ 正确' if actual_first_cell2 == expected_first_cell2 else '❌ 错误'})")

    # 创建校验结果DataFrame示例（使用场景1）
    result_df = df1[selected_columns1].copy()

    # 添加规则校验列（模拟）
    result_df['Rule1'] = ['通过', '失败']
    result_df['Rule2'] = ['跳过', '通过']
    result_df['Rule3'] = ['失败', '跳过']
    result_df['总校验结果'] = ['失败', '失败']

    print(f"\n最终校验结果页面预览（基于场景1）:")
    print(f"列数: {len(result_df.columns)}")
    print(f"行数: {len(result_df)}")
    print(f"\n列名:")
    for i, col in enumerate(result_df.columns, 1):
        if col.startswith('Rule') or col == '总校验结果':
            print(f"  {i:2d}. {col} (校验列)")
        else:
            print(f"  {i:2d}. {col} (数据列)")

    # 保存示例文件
    output_file = "校验结果页面示例.xlsx"
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        result_df.to_excel(writer, sheet_name='校验结果', index=False)

    print(f"\n✅ 示例文件已保存: {output_file}")

    # 验证结果
    success1 = (not has_data_source1 and
                len(cell_fields_in_result1) == 1 and
                actual_first_cell1 == expected_first_cell1)  # 场景1：不应该有数据源，只有1个小区字段，且是第一个

    success2 = (not has_data_source2 and
                len(cell_fields_in_result2) == 1 and
                actual_first_cell2 == expected_first_cell2)  # 场景2：不应该有数据源，只有1个小区字段，且是第一个

    print(f"\n=== 测试结果 ===")
    print(f"场景1（第一个小区字段是CU小区配置ID）: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"场景2（第一个小区字段是nrPhysicalCellDUId）: {'✅ 通过' if success2 else '❌ 失败'}")

    overall_success = success1 and success2
    print(f"总体测试结果: {'✅ 通过' if overall_success else '❌ 失败'}")

    return overall_success

def cleanup():
    """清理测试文件"""
    test_files = ["校验结果页面示例.xlsx"]
    for file in test_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"删除测试文件: {file}")
            except:
                print(f"无法删除文件: {file}")

if __name__ == "__main__":
    try:
        success = test_validation_columns()
        if success:
            print("\n🎉 测试成功！")
        else:
            print("\n⚠️ 测试失败！")
    finally:
        print("\n清理测试文件...")
        cleanup()
