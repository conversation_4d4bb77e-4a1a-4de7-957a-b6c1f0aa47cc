
import pandas as pd
import os, copy

special_fields = {
    "CU": ["CU小区配置ID"],
    "Phy_DU": ['DU物理小区ID'],
    "Carrier": ["NR载波ID", "NRCarrierId"],
    "DU": ['DU小区配置ID'],
}


def exclude_file(file_name: str):
    for item in ["GNB", "Replaceable", "SSBBeam", "results", "drop"]:
        if item in file_name:
            return True
    return False


def contains_element(new_df):
    for key, value in special_fields.items():
        for item in value:
            if item in new_df.columns:
                return key
    return None


def merge_excel_files_by_condition(folder_path, common_column, output_file_name="merged_output.xlsx"):
    all_dataframes = []
    files_name = []
    for filename in os.listdir(folder_path):
        if filename.endswith((".xls", ".xlsx")) and not filename.startswith("~$"):  # 过滤掉临时文件
            file_path = os.path.join(folder_path, filename)
            if exclude_file(filename):
                print(f'exclude_file{filename}')
                continue
            try:
                # 读取Excel文件
                df = pd.read_excel(file_path)
                if len(df):
                    all_dataframes.append(df)
                    files_name.append(filename)
                    print(f"成功读取文件: {filename}")
            except Exception as e:
                print(f"读取文件 {filename} 时发生错误: {e}")

    if not all_dataframes:
        print("没有找到任何Excel文件或无法读取任何文件。")
        return

    # 初始化合并后的DataFrame
    merged_df = pd.DataFrame()

    df_has_files = []
    # 如果只有一个文件，则直接使用该文件的数据
    if len(all_dataframes) == 1:
        merged_df = all_dataframes[0]
        print("只有一个Excel文件，无需合并。")
    else:
        merged_df = all_dataframes[0]
        if contains_element(merged_df):
            df_has_files.append(contains_element(merged_df))
            print("first", df_has_files)
        else:
            raise Exception("key not exist error")

        for i in range(1, len(all_dataframes)):
            new_df_field = contains_element(all_dataframes[i])
            if not new_df_field:
                raise Exception("errror key")
                continue
            a = copy.deepcopy(common_column)
            b = copy.deepcopy(common_column)
            if new_df_field in df_has_files:
                a.append(special_fields[new_df_field][0])
                # print(888, all_dataframes[i].columns.tolist()[:10])
                # print(6666, new_df_field in all_dataframes[i].columns)
                merged_df = pd.merge(merged_df, all_dataframes[i],
                                     left_on=a,
                                     right_on=a,
                                     how='inner',
                                     suffixes=(f'_file{i}', f'_file{i + 1}_new'))
            else:
                a.append(special_fields[df_has_files[0]][0])
                b.append(special_fields[new_df_field][0])
                merged_df = pd.merge(merged_df, all_dataframes[i],
                                     left_on=a,
                                     right_on=b,
                                     how='inner',
                                     suffixes=(f'_file{i}', f'_file{i + 1}_new'))  # 动态生成后缀，避免冲突
                df_has_files.append(new_df_field)
            print(f"与文件 {i + 1} 合并完成。")
            output_file_path = os.path.join(folder_path, output_file_name)
    try:
        merged_df.to_excel(output_file_path, index=False)  # index=False 不写入DataFrame的索引
        print(f"\n所有文件已成功合并到新文件: {output_file_path}")
    except Exception as e:
        print(f"保存文件时发生错误: {e}")
    # print(files_name)


# --- 使用示例 ---
if __name__ == "__main__":
    excel_folder = r"C:\Users\<USER>\Desktop\kpi\test"
    # '开始时间', '结束时间', '粒度', '子网ID', '管理网元',
    # commmon: '开始时间', '结束时间', '粒度', 'SubnetWork ID', '管理网元'
    # single:'DU物理小区ID',nrPhysicalCellDUId
    # NR载波ID,NRCarrierId
    # CU小区配置ID,cellId
    # DU小区配置ID
    common_condition_column = ['开始时间', '结束时间', '粒度', 'SubnetWork ID', '管理网元']
    merge_excel_files_by_condition(excel_folder, common_condition_column, "results数据.xlsx")


